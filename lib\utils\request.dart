import 'dart:async';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:dio/dio.dart' as dio;
import 'package:chilat2_mall_app/constants/config.dart';
import 'package:chilat2_mall_app/utils/auth_helper.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';

/*
  * 请求 操作类
  * 单例模式
  * 手册
  * https://github.com/flutterchina/dio/blob/master/README-ZH.md
  *
*/
class RequestUtil {
  static final RequestUtil _instance = RequestUtil._internal();
  factory RequestUtil() => _instance;

  late dio.Dio _dio;
  dynamic user;

  RequestUtil._internal() {
    // BaseOptions、Options、RequestOptions 都可以配置参数，优先级别依次递增，且可以根据优先级别覆盖参数
    dio.BaseOptions options = dio.BaseOptions(
      // 请求基地址,可以包含子路径
      baseUrl: SERVER_API_URL,
      //连接服务器超时时间，单位是毫秒.
      connectTimeout: const Duration(seconds: 50),

      // 响应流上前后两次接受到数据的间隔，单位为毫秒。
      receiveTimeout: const Duration(seconds: 50),

      // Http请求头.
      headers: {
        'Accept-Language': 'es',
      },

      /// 请求的Content-Type，默认值是"application/json; charset=utf-8".
      /// 如果您想以"application/x-www-form-urlencoded"格式编码请求数据,
      /// 可以设置此选项为 `Headers.formUrlEncodedContentType`,  这样[Dio]
      /// 就会自动编码请求体.
      contentType: Headers.jsonContentType,

      /// [responseType] 表示期望以那种格式(方式)接受响应数据。
      /// 目前 [ResponseType] 接受三种类型 `JSON`, `STREAM`, `PLAIN`.
      ///
      /// 默认值是 `JSON`, 当响应头中content-type为"application/json"时，dio 会自动将响应内容转化为json对象。
      /// 如果想以二进制方式接受响应数据，如下载一个二进制文件，那么可以使用 `STREAM`.
      ///
      /// 如果想以文本(字符串)格式接收响应数据，请使用 `PLAIN`.
      responseType: dio.ResponseType.json,
    );
    _dio = dio.Dio(options);

    // 添加拦截器
    _dio.interceptors.add(dio.InterceptorsWrapper(
      onRequest: (options, handler) async {
        final cookies = await CookieManager.getCookies();
        print("==>>TODO 请求数据: ${options.path}, ${options.data}");
        if (cookies?.isNotEmpty ?? false) {
          options.headers['Cookie'] = cookies;
        }
        return handler.next(options);
      },
      onResponse:
          (dio.Response response, dio.ResponseInterceptorHandler handler) {
        // 检查响应中是否包含403状态码
        final data = response.data;
        final handleAuthErrors =
            response.requestOptions.extra['handleAuthErrors'] ?? false;

        if (handleAuthErrors &&
            data is Map<String, dynamic> &&
            data.containsKey('result') &&
            data['result'] is Map) {
          final resultCode = data['result']['code'];
          print("==>>TODO 响应状态: ${response.requestOptions.uri} $resultCode");
          if (resultCode == 403) {
            final context = Get.context;
            if (context != null) {
              final currentRoute = Get.currentRoute;
              AuthHelper.showLoginModal(
                context,
                redirectRoute: currentRoute,
              );
            }
          }
        }

        return handler.next(response); // continue
      },
      onError: (dio.DioException e, dio.ErrorInterceptorHandler handler) {
        // 当请求失败时做一些预处理
        return handler.next(e);
      },
    ));
  }

  /*
   * 获取token
   */
  Future<String> getAuthorizationHeader() async {
    try {
      var user = LocalStorage().getJSON(USER_INFO);
      return user != null && user['token'] != null && user['token'] != ''
          ? 'token=${user['token']}'
          : "";
    } catch (e) {
      return "";
    }
  }

  /// get 操作
  Future get(
    String path, {
    dynamic params,
    dio.Options? options,
    bool handleAuthErrors = false, // 是否处理授权错误
  }) async {
    try {
      dio.Options requestOptions = options ?? dio.Options();

      // 设置是否处理授权错误的标志
      requestOptions.extra = {
        ...requestOptions.extra ?? {},
        'handleAuthErrors': handleAuthErrors,
      };

      /// 以下三行代码为获取token然后将其合并到header的操作
      Map<String, dynamic> authorization = {
        "token": await getAuthorizationHeader()
      };
      requestOptions = requestOptions.copyWith(headers: authorization);
      var response = await _dio.get(
        path,
        queryParameters: params,
        options: requestOptions,
      );
      return response.data;
    } on dio.DioException catch (e) {
      throw createErrorEntity(e);
    }
  }

  ///  post 操作
  Future post(
    String path, {
    dynamic params,
    dio.Options? options,
    bool handleAuthErrors = true, // 是否处理授权错误
  }) async {
    try {
      dio.Options requestOptions = options ?? dio.Options();

      // 设置是否处理授权错误的标志
      requestOptions.extra = {
        ...requestOptions.extra ?? {},
        'handleAuthErrors': handleAuthErrors,
      };

      /// 以下三行代码为获取token然后将其合并到header的操作
      var token = await getAuthorizationHeader();
      requestOptions = requestOptions.copyWith(headers: {"Cookie": token});
      var response =
          await _dio.post(path, data: params, options: requestOptions);

      return response.data;
    } on dio.DioException catch (e) {
      throw createErrorEntity(e);
    }
  }

  ///  put 操作
  Future put(
    String path, {
    dynamic params,
    dio.Options? options,
    bool handleAuthErrors = false, // 是否处理授权错误
  }) async {
    try {
      dio.Options requestOptions = options ?? dio.Options();

      // 设置是否处理授权错误的标志
      requestOptions.extra = {
        ...requestOptions.extra ?? {},
        'handleAuthErrors': handleAuthErrors,
      };

      /// 以下三行代码为获取token然后将其合并到header的操作
      var token = await getAuthorizationHeader();
      requestOptions = requestOptions.copyWith(headers: {"Cookie": token});
      var response =
          await _dio.put(path, data: params, options: requestOptions);
      return response.data;
    } on dio.DioException catch (e) {
      throw createErrorEntity(e);
    }
  }

  ///  patch 操作
  Future patch(
    String path, {
    dynamic params,
    dio.Options? options,
    bool handleAuthErrors = false, // 是否处理授权错误
  }) async {
    try {
      dio.Options requestOptions = options ?? dio.Options();

      // 设置是否处理授权错误的标志
      requestOptions.extra = {
        ...requestOptions.extra ?? {},
        'handleAuthErrors': handleAuthErrors,
      };

      /// 以下三行代码为获取token然后将其合并到header的操作
      var token = await getAuthorizationHeader();
      requestOptions = requestOptions.copyWith(headers: {"Cookie": token});
      var response =
          await _dio.patch(path, data: params, options: requestOptions);
      return response.data;
    } on dio.DioException catch (e) {
      throw createErrorEntity(e);
    }
  }

  /// delete 操作
  Future delete(
    String path, {
    dynamic params,
    dio.Options? options,
    bool handleAuthErrors = false, // 是否处理授权错误
  }) async {
    try {
      dio.Options requestOptions = options ?? dio.Options();

      // 设置是否处理授权错误的标志
      requestOptions.extra = {
        ...requestOptions.extra ?? {},
        'handleAuthErrors': handleAuthErrors,
      };

      /// 以下三行代码为获取token然后将其合并到header的操作
      var token = await getAuthorizationHeader();
      requestOptions = requestOptions.copyWith(headers: {"Cookie": token});
      var response =
          await _dio.delete(path, data: params, options: requestOptions);
      return response.data;
    } on dio.DioException catch (e) {
      throw createErrorEntity(e);
    }
  }

  ///  post form 表单提交操作
  Future postForm(
    String path, {
    dynamic params,
    dio.Options? options,
    bool handleAuthErrors = false, // 是否处理授权错误
  }) async {
    try {
      dio.Options requestOptions = options ?? dio.Options();

      // 设置是否处理授权错误的标志
      requestOptions.extra = {
        ...requestOptions.extra ?? {},
        'handleAuthErrors': handleAuthErrors,
      };

      /// 以下三行代码为获取token然后将其合并到header的操作
      var token = await getAuthorizationHeader();
      requestOptions = requestOptions.copyWith(headers: {"Cookie": token});
      var response = await _dio.post(path,
          data: dio.FormData.fromMap(params), options: requestOptions);
      return response.data;
    } on dio.DioException catch (e) {
      throw createErrorEntity(e);
    }
  }
}

/*
   * error统一处理
   */
// 错误信息
ErrorEntity createErrorEntity(dio.DioException error) {
  switch (error.type) {
    case dio.DioExceptionType.cancel:
      {
        return ErrorEntity(code: -1, message: "请求取消");
      }
    case dio.DioExceptionType.connectionTimeout:
      {
        return ErrorEntity(code: -1, message: "连接超时");
      }

    case dio.DioExceptionType.sendTimeout:
      {
        return ErrorEntity(code: -1, message: "请求超时");
      }

    case dio.DioExceptionType.receiveTimeout:
      {
        return ErrorEntity(code: -1, message: "响应超时");
      }
    case dio.DioExceptionType.badResponse:
      {
        try {
          int? errCode = error.response?.statusCode;
          if (errCode == null) {
            return ErrorEntity(code: -2, message: error.message);
          }
          switch (errCode) {
            case 400:
              {
                return ErrorEntity(code: errCode, message: "请求语法错误");
              }
            case 401:
              {
                return ErrorEntity(code: errCode, message: "没有权限");
              }

            case 403:
              {
                return ErrorEntity(code: errCode, message: "服务器拒绝执行");
              }

            case 404:
              {
                return ErrorEntity(code: errCode, message: "无法连接服务器");
              }

            case 405:
              {
                return ErrorEntity(code: errCode, message: "请求方法被禁止");
              }

            case 500:
              {
                return ErrorEntity(code: errCode, message: "服务器内部错误");
              }

            case 502:
              {
                return ErrorEntity(code: errCode, message: "无效的请求");
              }

            case 503:
              {
                return ErrorEntity(code: errCode, message: "服务器挂了");
              }

            case 505:
              {
                return ErrorEntity(code: errCode, message: "不支持HTTP协议请求");
              }

            default:
              {
                // return ErrorEntity(code: errCode, message: "未知错误");
                return ErrorEntity(
                    code: errCode,
                    message: error.response?.statusMessage ?? '');
              }
          }
        } on Exception catch (_) {
          return ErrorEntity(code: -1, message: "未知错误");
        }
      }

    default:
      {
        return ErrorEntity(code: -1, message: error.message);
      }
  }
}

// 异常处理
class ErrorEntity implements Exception {
  int code;
  String? message;
  ErrorEntity({required this.code, this.message});

  @override
  String toString() {
    if (message == null) return "Exception";
    return "Exception: code $code, $message";
  }
}
