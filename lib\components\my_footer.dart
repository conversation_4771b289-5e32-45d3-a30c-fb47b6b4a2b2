import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/components/common_webview_page.dart';
import 'package:chilat2_mall_app/utils/auth_helper.dart';

class MyFooter extends StatelessWidget {
  const MyFooter({super.key});

  // 社交媒体图标列表
  final List<IconItem> _iconList = const [
    IconItem(
        href:
            "https://www.facebook.com/profile.php?id=61563281785563&mibextid=LQQJ4d",
        src: "assets/images/icons/facebook.png",
        title: "Facebook"),
    IconItem(
        href: "http://www.linkedin.com/in/chilat-shop-22904831a",
        src: "assets/images/icons/in.png",
        title: "LinkedIn"),
    IconItem(
        href:
            "https://www.instagram.com/chilatshop_oficial?igsh=MXB4ZWZlOG8wdGYyaQ==",
        src: "assets/images/icons/ins.png",
        title: "Instagram"),
    IconItem(
        href: "https://www.youtube.com/@ChilatShop",
        src: "assets/images/icons/youtube.png",
        title: "YouTube"),
    IconItem(
        href: "https://www.tiktok.com/@chilatshop?_t=8oJF69Z5Xlp&_r=1",
        src: "assets/images/icons/tiktok.png",
        title: "TikTok"),
    IconItem(
        href: "https://www.youtube.com/@Chilatshop-tutorial",
        src: "assets/images/icons/videos.png",
        title: "Tutoriales"),
  ];

  // 品牌列表
  final List<String> _brandList = const [
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/c3f4726c-72bb-4b6d-bf58-aa0e0019c85a.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/eccab92a-e50c-4fcb-8fe6-71c6272dff11.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/10df6bc2-6f1e-4d62-8f77-4e1938f0cb3f.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/82efd70a-9999-4a39-b379-0056244d56ed.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/fec58d91-daf4-443f-bbab-42986696609f.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/1a429d67-f273-4956-a9e4-717b840885a1.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/4c73c6b3-b79e-468b-a295-25c9256766e2.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/ca007192-e294-487a-825f-db9ce89141c5.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/d469d4cf-9e91-43e3-9d0c-8dcb0033b421.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/baadd159-c93b-478d-9a81-dbc8618553d7.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/762bf491-b80b-4eae-af50-4235305d12df.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/a54baa22-2353-45cc-9d20-3f97f1cffd57.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/1eb409a6-43bf-4c02-ac2e-0921b3018c41.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/89fe5271-3ae0-4acb-9c75-88228b6fd176.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/4214386b-966b-4f73-9ad4-7ad3eeea5daf.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/4858c059-c506-4744-83e4-c86aa43de80b.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/f56d8c3f-becd-43c4-bc28-0b7b26631f62.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/79b18fa6-5a96-419c-85ec-3f72f3e4f71d.png",
    "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/71271e5d-13a3-41c8-9075-23555141c480.png",
  ];

  // 使用WebView打开URL
  void _openInWebView(String url, String title) {
    Get.to(() => CommonWebViewPage(
          url: url,
          title: title,
          showAppBar: true,
          showLoading: true,
        ));
  }

  // 创建带有点击功能的文本项
  Widget _buildTextItem(BuildContext context, String textKey, String routeName,
      {String? articleCode}) {
    return Container(
      margin: EdgeInsets.only(top: 6.sp),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (articleCode != null) {
              Get.toNamed(routeName, arguments: {'articleCode': articleCode});
            } else {
              Get.toNamed(routeName);
            }
          },
          child: Text(
            I18n.of(context)!.translate(textKey),
            style: TextStyle(
              fontSize: 13.sp,
              color: AppColors.primaryGreyText,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final leftWidth = (screenWidth - 32.sp) * 0.55;
    final rightWidth = (screenWidth - 32.sp) * 0.45;

    return Container(
      color: Colors.grey.shade100,
      padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 30.sp),
      child: Column(
        children: [
          // 第一行：联系我们和关于我们部分
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 联系我们区域
              Container(
                width: leftWidth,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      I18n.of(context)!.translate('cm_column.contactUs'),
                      style: TextStyle(
                          fontSize: 13.sp, fontWeight: FontWeight.w600),
                    ),
                    Container(
                      padding: EdgeInsets.only(top: 2.sp),
                      child: Text(
                        "<EMAIL>",
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: AppColors.primaryGreyText,
                        ),
                      ),
                    ),
                    Obx(() => Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () {
                              if (Global.isLogin.value) {
                                NavigatorUtil.pushNamed(
                                    context, AppRoutes.MinePage);
                              } else {
                                AuthHelper.showRegisterModal(
                                  context,
                                );
                              }
                            },
                            child: Container(
                              margin: EdgeInsets.only(top: 8.sp),
                              padding: EdgeInsets.symmetric(
                                vertical: 6.sp,
                                horizontal: 14.sp, // 水平内边距控制最小宽度
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primaryColor,
                                borderRadius: BorderRadius.circular(4.sp),
                              ),
                              child: Text(
                                Global.isLogin.value
                                    ? I18n.of(context)!
                                        .translate('cm_common_myAccount')
                                    : I18n.of(context)!
                                        .translate('cm_common_registerNow'),
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12.sp,
                                ),
                              ),
                            ),
                          ),
                        )),
                  ],
                ),
              ),
              // 关于我们区域
              Container(
                width: rightWidth,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      I18n.of(context)!.translate('cm_column.aboutUs'),
                      style: TextStyle(
                          fontSize: 13.sp, fontWeight: FontWeight.w600),
                    ),
                    _buildTextItem(
                        context, 'cm_news.aboutUs', AppRoutes.AboutUs),
                    _buildTextItem(context, 'cm_news.askedQuestions',
                        AppRoutes.FrequentlyQuestions),
                    _buildTextItem(context, 'cm_news.blog', AppRoutes.BlogPage),
                    _buildTextItem(context, 'cm_news.invitedReward',
                        AppRoutes.InvitedReward),
                  ],
                ),
              ),
            ],
          ),
          // 第二行：公司政策和帮助部分
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 公司政策区域
                Container(
                  width: leftWidth,
                  padding: EdgeInsets.symmetric(vertical: 18.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        I18n.of(context)!.translate('cm_column.companyPolicy'),
                        style: TextStyle(
                            fontSize: 13.sp, fontWeight: FontWeight.w600),
                      ),
                      _buildTextItem(context, 'cm_news.commission',
                          AppRoutes.ArticleCommission),
                      _buildTextItem(context, 'cm_news.warrantyService',
                          AppRoutes.ArticlePage,
                          articleCode: '10002'),
                      _buildTextItem(context, 'cm_news.privacyPolicy',
                          AppRoutes.ArticlePage,
                          articleCode: '10001'),
                      _buildTextItem(context, 'cm_news.termsOfService',
                          AppRoutes.ArticlePage,
                          articleCode: '10004'),
                    ],
                  ),
                ),
                // 帮助区域
                Container(
                  width: rightWidth,
                  padding: EdgeInsets.symmetric(vertical: 18.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        I18n.of(context)!.translate('cm_column.help'),
                        style: TextStyle(
                            fontSize: 13.sp, fontWeight: FontWeight.w600),
                      ),
                      _buildTextItem(
                          context, 'cm_news.helpCenter', AppRoutes.HelpCenter),
                      _buildTextItem(
                          context, 'cm_news.quickGuide', AppRoutes.QuickGuide),
                      _buildTextItem(context, 'cm_news.paymentMethods',
                          AppRoutes.PaymentMethods),
                      Container(
                        margin: EdgeInsets.only(top: 6.sp),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => _openInWebView(
                                'https://www.youtube.com/@Chilatshop-tutorial',
                                'Tutoriales'),
                            child: Text(
                              I18n.of(context)!
                                  .translate('cm_news.chilatshopTutorials'),
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: AppColors.primaryGreyText,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ]),
          // 第三行：品牌和社交媒体图标
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 品牌列表
              Container(
                width: leftWidth,
                padding: EdgeInsets.only(top: 12.sp, right: 12.sp),
                child: Wrap(
                  spacing: 2.sp,
                  runSpacing: 8.sp,
                  children: _brandList.map((String item) {
                    return Material(
                      color: Colors.transparent,
                      child: InkWell(
                        child: MyCachedNetworkImage(
                          height: 16.sp,
                          imageUrl: item,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
              // 社交媒体图标
              Container(
                width: rightWidth,
                padding: EdgeInsets.only(top: 12.sp),
                child: Wrap(
                  spacing: 6.sp,
                  children: _iconList.map((IconItem item) {
                    return Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () => _openInWebView(item.href, item.title),
                        child: Image(
                          image: AssetImage(item.src),
                          width: 20.sp,
                          fit: BoxFit.fill,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}

class IconItem {
  final String href;
  final String src;
  final String title;

  const IconItem({
    required this.href,
    required this.src,
    required this.title,
  });
}
