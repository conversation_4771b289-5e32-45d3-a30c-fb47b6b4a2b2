import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

/// 基于WebView的YouTube视频模态框组件 - 解决SSL和渲染问题
class WebViewYouTubeModal extends StatefulWidget {
  final String videoId;
  final String? title;

  const WebViewYouTubeModal({
    Key? key,
    required this.videoId,
    this.title,
  }) : super(key: key);

  @override
  State<WebViewYouTubeModal> createState() => _WebViewYouTubeModalState();
}

class _WebViewYouTubeModalState extends State<WebViewYouTubeModal> {
  late WebViewController _controller;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    // 创建优化的YouTube嵌入URL
    final embedUrl = _buildEmbedUrl();

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.black)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
              });
            }
          },
          onPageFinished: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }
          },
          onWebResourceError: (WebResourceError error) {
            print('YouTube WebView error: ${error.description}');
            if (mounted) {
              setState(() {
                _isLoading = false;
                _hasError = true;
              });
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            // 只允许YouTube相关的导航
            if (request.url.contains('youtube.com') || 
                request.url.contains('googlevideo.com') ||
                request.url.contains('ytimg.com')) {
              return NavigationDecision.navigate;
            }
            return NavigationDecision.prevent;
          },
        ),
      );

    // 设置用户代理，提高兼容性
    _controller.setUserAgent(
      'Mozilla/5.0 (Linux; Android 10; Mobile) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36'
    );

    // 加载URL
    _controller.loadRequest(Uri.parse(embedUrl));
  }

  String _buildEmbedUrl() {
    // 构建优化的YouTube嵌入URL
    final params = {
      'autoplay': '0',           // 不自动播放
      'controls': '1',           // 显示控制栏
      'showinfo': '0',           // 不显示视频信息
      'rel': '0',                // 不显示相关视频
      'modestbranding': '1',     // 简化品牌显示
      'playsinline': '1',        // 内联播放
      'enablejsapi': '1',        // 启用JavaScript API
      'fs': '1',                 // 允许全屏
      'cc_load_policy': '0',     // 不显示字幕
      'iv_load_policy': '3',     // 不显示视频注释
      'origin': 'https://flutter.dev', // 设置origin
    };

    final queryString = params.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');

    return 'https://www.youtube.com/embed/${widget.videoId}?$queryString';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.all(16),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        height: MediaQuery.of(context).size.width * 0.95 * 9 / 16 + 60,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            // WebView容器
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                child: _buildContent(),
              ),
            ),
            
            // 关闭按钮
            Positioned(
              top: 8,
              right: 8,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),

            // 标题（可选）
            if (widget.title != null)
              Positioned(
                bottom: 8,
                left: 8,
                right: 48,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    widget.title!,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_hasError) {
      return _buildErrorWidget();
    } else if (_isLoading) {
      return _buildLoadingWidget();
    } else {
      return WebViewWidget(controller: _controller);
    }
  }

  /// 构建加载指示器
  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: Colors.red,
              strokeWidth: 3,
            ),
            SizedBox(height: 16),
            Text(
              '正在加载视频...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建错误提示
  Widget _buildErrorWidget() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              '视频加载失败',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '请检查网络连接或稍后重试',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _hasError = false;
                  _isLoading = true;
                });
                _initializeWebView();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text('重试'),
            ),
          ],
        ),
      ),
    );
  }
}

/// WebView YouTube模态框帮助类
class WebViewYouTubeHelper {
  /// 显示YouTube视频模态框
  static Future<void> showVideoModal(
    BuildContext context, {
    required String videoId,
    String? title,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.8),
      builder: (BuildContext context) {
        return WebViewYouTubeModal(
          videoId: videoId,
          title: title,
        );
      },
    );
  }

  /// 从YouTube URL提取视频ID
  static String? extractVideoId(String url) {
    final regExp = RegExp(
      r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
      caseSensitive: false,
    );
    final match = regExp.firstMatch(url);
    return match?.group(1);
  }

  /// 验证视频ID格式
  static bool isValidVideoId(String videoId) {
    return RegExp(r'^[a-zA-Z0-9_-]{11}$').hasMatch(videoId);
  }
}
