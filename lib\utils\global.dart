import 'dart:io';

import 'package:chilat2_mall_app/constants/config.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// 全局配置
class Global {
  /// 响应式登录状态
  static final RxBool isLogin = false.obs;
  static final RxBool countrySelected = false.obs;
  static PageClickIncomingModel? incomingModel;
  static const TextStyle textColor101 = TextStyle(color: Colors.black);
  static const TextStyle textColor102 = TextStyle(color: Colors.black87);
  static const TextStyle textColor103 = TextStyle(color: Colors.black54);
  static const TextStyle textColor104 = TextStyle(color: Colors.white);
  static const TextStyle textColor105 = TextStyle(color: Colors.grey);
  static const TextStyle textWeight101 = TextStyle(fontWeight: FontWeight.w500);
  static const TextStyle textWeight102 =
      TextStyle(fontSize: 18, fontWeight: FontWeight.w500);
  static const TextStyle textWeight103 =
      TextStyle(fontSize: 16, fontWeight: FontWeight.w500);
  static const TextStyle textSize101 =
      TextStyle(fontSize: 16, color: Colors.white);

  // static const TextStyle textSize102 =
  //     TextStyle(fontSize: 16, fontWeight: FontWeight.w500);
  static const TextStyle textSize103 =
      TextStyle(fontSize: 18, color: Colors.white);
  static const TextStyle textSize104 =
      TextStyle(fontSize: 12, color: Colors.black54);
  // static const TextStyle textSize105 =
  //     TextStyle(fontSize: 12, color: Colors.white);
  static const TextStyle textSize106 =
      TextStyle(fontSize: 16, color: Colors.grey);

  /// init
  static Future init() async {
    // 本地存储初始化
    await LocalStorage.init();

    // 初始化登录状态
    await updateLoginStatus();

    // await setCartList();

    // android 状态栏为透明的沉浸
    if (Platform.isAndroid) {
      SystemUiOverlayStyle systemUiOverlayStyle =
          SystemUiOverlayStyle(statusBarColor: Colors.transparent);
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
    }
  }

  static Future<PageClickIncomingModel> getIncomingConfig() async {
    dynamic res = await LocalStorage().getJSON(INCOMING_CONFIG);
    if (res != null && res['siteId'] != null) {
      return PageClickIncomingModel.fromJson(res);
    }

    res = await BasisAPI.useGetNuxtConfig(
        {'requestUri': Get.currentRoute, 'abtestPage': 'homepage2409'});
    if (res != null && res?['result']?['code'] == 200) {
      incomingModel = PageClickIncomingModel.fromJson(res['data']);
      if (incomingModel!.siteId == null &&
          incomingModel!.siteList!.isNotEmpty) {
        incomingModel!.siteId = incomingModel!.siteList?[0].id;
      }
    } else {
      incomingModel = PageClickIncomingModel(); // 提供默认实例，如适用
    }
    return incomingModel!;
  }

  static Future<void> setIncomingConfig() async {
    bool result = await LocalStorage().remove(INCOMING_CONFIG);
    if (result) {
      await getIncomingConfig();
    }
  }

  // 购物车信息
  static Future<MidCartModel?> setCartList({dynamic data}) async {
    dynamic user = LocalStorage().getJSON(USER_INFO);
    if (user == null) {
      LocalStorage().setJSON(CART_DATA, null);
      LocalStorage().setJSON(INQUIRY_LIST, null);
    }

    if (data != null) {
      LocalStorage().setJSON(CART_DATA, data);
      return MidCartModel.fromJson(data);
    }

    if (isLogin.value == false) {
      return null;
    }

    dynamic res = await InquiryAPI.useGetCart({});
    if (res != null && res?['result']?['code'] == 200) {
      LocalStorage().setJSON(CART_DATA, res['data']);
      return MidCartModel.fromJson(res['data']);
    } else {
      return null;
    }
  }

  static Future<MidCartModel?> getCartData() async {
    dynamic res = LocalStorage().getJSON(CART_DATA);
    if (res != null) {
      return MidCartModel.fromJson(res);
    }

    return null;
  }

  // 询价信息
  static Future<void> setInquiryInfo({dynamic data}) async {
    LocalStorage().setJSON(INQUIRY_LIST, data);
  }

  static Future<dynamic> getInquiryInfo() async {
    dynamic data = LocalStorage().getJSON(INQUIRY_LIST);
    return data;
  }

  // 用户信息
  static Future<dynamic> setUserInfo(dynamic data) async {
    dynamic user = LocalStorage().setJSON(USER_INFO, data);
    return user;
  }

  static Future<AuthLoginModel?> getUserInfo() async {
    dynamic user = LocalStorage().getJSON(USER_INFO);
    if (user == null) {
      return null;
    }
    return AuthLoginModel.fromJson(user);
  }

  static Future<UserModel?> getUserDetail() async {
    dynamic user = LocalStorage().getJSON(USER_DETAIL);
    print("==>>TODO 1105: $user");
    return UserModel.fromJson(user);
  }

  // 更新登录状态
  static Future<void> updateLoginStatus() async {
    dynamic user = LocalStorage().getJSON(USER_INFO);

    // 更新响应式变量
    isLogin.value =
        user != null && user['token'] != null && user['token'] != '';

    print("登录状态更新: ${isLogin.value}");
  }

  // 站点
  static Future<void> setSiteData(SiteListModel? siteData) async {
    if (siteData != null) {
      CookieManager.saveSiteID(siteData.id.toString());
      LocalStorage().setJSON(SITE_DATA, siteData.toJson());
    }
  }

  static Future<SiteListModel?> getSiteData() async {
    dynamic siteData = LocalStorage().getJSON(SITE_DATA);
    if (siteData != null) {
      return SiteListModel.fromJson(siteData);
    }
    return null;
  }
}
