// 佣金
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/my_search_bar.dart';
import 'package:flutter/material.dart';

class CommissionPage extends StatefulWidget {
  const CommissionPage({super.key});

  @override
  State<CommissionPage> createState() => _CommissionPageState();
}

class _CommissionPageState extends State<CommissionPage> {
  double screenWidth = 0.0;
  final bool _isLoading = false;
  bool _showBackToTopButton = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      setState(() {
        _showBackToTopButton = _scrollController.offset > 150;
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void onScrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;

    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            backgroundColor: Colors.white,
            body: Stack(
              children: [
                SingleChildScrollView(
                    controller: _scrollController,
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          MySearchBar(homePage: true),
                          Container(
                            padding: EdgeInsets.only(
                                top: 16, bottom: 52, left: 10, right: 10),
                            child: Column(
                              children: [
                                Container(
                                  padding: EdgeInsets.only(top: 32),
                                  child: Center(
                                    child: Text(
                                      'COMISIONES DE CHILAT SHOP',
                                      style: TextStyle(
                                          fontSize: 18,
                                          color: Color(0xFF333333)),
                                    ),
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 32, vertical: 8),
                                  child: Center(
                                    child: Text(
                                      'AUMENTA EL VOLUMEN DE COMPRA Y PAGA MENOS COMISIÓN',
                                      style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFF333333)),
                                    ),
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.only(top: 12),
                                  child: Text(
                                      'Cobraremos una comisión basada en el valor total de los productos del pedido.',
                                      style: TextStyle(
                                        fontSize: 12,
                                      )),
                                ),
                                Container(
                                  padding: EdgeInsets.only(top: 12),
                                  child: Text(
                                      'La tasa de comisión se determina en función del valor de los productos que haya comprado. <br /> (Valor de la mercancía = Cantidad * Precio unitario).',
                                      style: TextStyle(
                                        fontSize: 12,
                                      )),
                                ),
                                Container(
                                  padding: EdgeInsets.only(top: 12),
                                  child: Text(
                                      'Por ejemplo, si compra artículos con un valor de pedido único superior a 30,000 \$, la tasa de comisión de su pedido será del 5%. (Comisión = valor de compra * tasa de comisión)',
                                      style: TextStyle(
                                        fontSize: 12,
                                      )),
                                ),
                                Container(
                                  padding: EdgeInsets.only(top: 12),
                                  child: Text(
                                      'Consulte la tabla siguiente para ver el porcentaje correspondiente al valor de los bienes adquiridos:',
                                      style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade500)),
                                ),
                                Container(
                                    margin: EdgeInsets.only(top: 12),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            color: Colors
                                                .red.shade700, // 设置背景颜色为红色
                                            borderRadius: BorderRadius.circular(
                                                4), // 设置圆角半径为 10
                                          ),
                                          child: Row(
                                            children: [
                                              Container(
                                                width: screenWidth * 0.2,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 6, vertical: 8),
                                                child: Center(
                                                  child: Text(
                                                    "Nivel",
                                                    style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 11),
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                width: screenWidth * 0.3,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 6, vertical: 8),
                                                child: Center(
                                                  child: Text(
                                                    "Tasa de comisión",
                                                    style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 11),
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                width: screenWidth * 0.43,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 6, vertical: 8),
                                                child: Center(
                                                  child: Text(
                                                    "Valor de la compra (USD)",
                                                    style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 11),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Row(
                                          children: [
                                            Container(
                                              width: screenWidth * 0.2,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 6, vertical: 8),
                                              child: Center(
                                                child: MyCachedNetworkImage(
                                                    height: 32,
                                                    width: 32,
                                                    imageUrl:
                                                        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/19/5b7521b2-9d25-4fbc-bc99-21d0f082999c.png"),
                                              ),
                                            ),
                                            Container(
                                              width: screenWidth * 0.3,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 6, vertical: 8),
                                              child: Center(
                                                child: Text(
                                                  "5%",
                                                  style:
                                                      TextStyle(fontSize: 12),
                                                ),
                                              ),
                                            ),
                                            Container(
                                              width: screenWidth * 0.43,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 6, vertical: 8),
                                              child: Center(
                                                child: Text(
                                                  "+\$30,000",
                                                  style:
                                                      TextStyle(fontSize: 12),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: Colors
                                                .grey.shade400, // 设置背景颜色为红色
                                            borderRadius: BorderRadius.circular(
                                                4), // 设置圆角半径为 10
                                          ),
                                          child: Row(
                                            children: [
                                              Container(
                                                width: screenWidth * 0.2,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 6, vertical: 8),
                                                child: Center(
                                                  child: MyCachedNetworkImage(
                                                      height: 32,
                                                      width: 32,
                                                      imageUrl:
                                                          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/19/f3281b61-2e52-4105-a203-7a85d7e77c89.png"),
                                                ),
                                              ),
                                              Container(
                                                width: screenWidth * 0.3,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 6, vertical: 8),
                                                child: Center(
                                                  child: Text(
                                                    "6%",
                                                    style:
                                                        TextStyle(fontSize: 12),
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                width: screenWidth * 0.43,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 6, vertical: 8),
                                                child: Center(
                                                  child: Text(
                                                    "+\$20,000",
                                                    style:
                                                        TextStyle(fontSize: 12),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Row(
                                          children: [
                                            Container(
                                              width: screenWidth * 0.2,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 6, vertical: 8),
                                              child: Center(
                                                child: MyCachedNetworkImage(
                                                    height: 32,
                                                    width: 32,
                                                    imageUrl:
                                                        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/19/a43b29e6-f248-44d1-934f-4196ce43ea2c.png"),
                                              ),
                                            ),
                                            Container(
                                              width: screenWidth * 0.3,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 6, vertical: 8),
                                              child: Center(
                                                child: Text(
                                                  "7%",
                                                  style:
                                                      TextStyle(fontSize: 12),
                                                ),
                                              ),
                                            ),
                                            Container(
                                              width: screenWidth * 0.43,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 6, vertical: 8),
                                              child: Center(
                                                child: Text(
                                                  "+\$10,000",
                                                  style:
                                                      TextStyle(fontSize: 12),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: Colors
                                                .grey.shade400, // 设置背景颜色为红色
                                            borderRadius: BorderRadius.circular(
                                                4), // 设置圆角半径为 10
                                          ),
                                          child: Row(
                                            children: [
                                              Container(
                                                width: screenWidth * 0.2,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 6, vertical: 8),
                                                child: Center(
                                                  child: MyCachedNetworkImage(
                                                      height: 32,
                                                      width: 32,
                                                      imageUrl:
                                                          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/19/8d1edeb4-dd70-49bf-8c1e-59a650e80ac6.png"),
                                                ),
                                              ),
                                              Container(
                                                width: screenWidth * 0.3,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 6, vertical: 8),
                                                child: Center(
                                                  child: Text(
                                                    "8%",
                                                    style:
                                                        TextStyle(fontSize: 12),
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                width: screenWidth * 0.43,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 6, vertical: 8),
                                                child: Center(
                                                  child: Text(
                                                    "+\$0",
                                                    style:
                                                        TextStyle(fontSize: 12),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        )
                                      ],
                                    )),
                              ],
                            ),
                          ),
                          MyFooter()
                        ])),
                if (_showBackToTopButton)
                  Positioned(
                    bottom: 20,
                    right: 20,
                    child: FloatingActionButton(
                      onPressed: onScrollToTop,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(64),
                      ),
                      child: const Icon(Icons.arrow_upward),
                    ),
                  ),
              ],
            ),
          );
  }
}
