// ignore_for_file: constant_identifier_names

part of 'app_pages.dart';

abstract class AppRoutes {
  static const SplashPage = '/splash_page';
  static const LoginPage = '/login';
  static const Logup = '/logup';
  static const HomePage = '/home_page';
  static const CategoryPage = '/category_page';
  static const ChooseAddress = '/choose_address';
  static const ConfirmOrder = '/confirm_order';
  static const Detail = '/detail';
  static const MinePage = '/mine';
  static const MineSettingPage = '/mine_setting_page';
  static const MineOrderPage = '/mine_order_page';
  static const MineInquiryPage = '/mine_inquiry_page';
  static const MineAddressPage = '/mine_address_page';
  static const MineCouponPage = '/mine_coupon_page';
  static const MineInvitePage = '/mine_invite_page';
  static const OrderDetail = '/order_detail';
  static const UpdatePassword = '/login/update_password';
  static const ModifyPassword = '/login/modify_password';
  static const Register = '/login/register';
  static const RegisterSuccess = '/login/register_success';
  static const Terms = '/login/terms';
  static const OrderPayment = '/order_payment';
  static const OrderPayResults = '/order/payment-results';
  static const Search = '/search';
  static const ShoppingCart = '/shoppingcart';
  static const Supplier = '/supplier';
  static const ProductPage = '/product_page';
  static const ProductList = '/product_list';
  static const FindSubmitThankyou = '/find/submit_thankyou';
  // 询盘提交页
  static const FindSubmit = '/find/submit';
  static const SearchLooking = '/search/looking';
  // 购物车列表
  static const CartPage = '/cart_page';
  static const SearchSubmitThankYou = '/search/submit-thankyou';
  // 普通文章
  static const ArticlePage = '/article/article_page';
  // 关于我们
  static const AboutUs = '/article/about_us';
  // 常见问题列表
  static const FrequentlyQuestions = '/article/frequently_questions';
  // 常见问题
  static const ArticleFAQ = '/article/faq';
  // 博客列表
  static const BlogPage = '/article/blog';
  // 老带新
  static const InvitedReward = '/article/invited-reward';
  // 佣金
  static const ArticleCommission = '/article/commission';
  // 帮助中心
  static const HelpCenter = '/article/help-center';
  // 快速指南
  static const QuickGuide = '/article/quick-guide';
  // 支付方式说明
  static const PaymentMethods = '/article/payment-methods';

  // notfound
  static const NotFound = '/notfound';

  static const String AuthBlockPage = '/common/auth_block_page';
  // 活动页面
  static const String ActivityPage = '/activity_page';
  //
}
