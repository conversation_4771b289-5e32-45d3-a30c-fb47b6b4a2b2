import 'package:chilat2_mall_app/components/components.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import './order_detail_controller.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import './components/coupon_detail.dart';
import 'package:chilat2_mall_app/styles/styles.dart';

class OrderDetailPage extends GetView<OrderDetailController> {
  const OrderDetailPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // 返回订单列表
        Get.back();
        return false;
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF2F2F2),
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Get.back(),
          ),
          title: Text(
              I18n.of(context)?.translate('cm_order.orderDetailTitle') ??
                  'Detalles del pedido'),
          centerTitle: true,
        ),
        body: controller.obx(
          (state) => _buildBody(context),
          onLoading: const Center(child: CircularProgressIndicator()),
          onError: (error) => Center(child: Text(error ?? '')),
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Stack(
      children: [
        RefreshIndicator(
          onRefresh: () async {
            await controller.getOrderDetail();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              children: [
                _buildStatusCard(context), //订单状态
                _buildAddressCard(context), //地址信息
                _buildTransportCard(context), //运费信息
                _buildOrderInfoCard(context), //订单商品信息
                _buildOrderFeeCard(context), //订单费用明细
                _buildBillFeeCard(context), //账单费用明细
                _buildPaymentCard(context), //待付款信息
                _buildMerchantRemarkCard(context), //商家备注
                _buildCustomerRemarkCard(context), //客户备注
                if (controller.isPayFee.value || controller.isOfflinePayment)
                  SizedBox(height: 140.sp)
                else
                  SizedBox(height: 40.sp),
              ],
            ),
          ),
        ),
        _buildBottomPaymentBar(context), //底部支付栏
      ],
    );
  }

  Widget _buildStatusCard(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 56.sp,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: CachedNetworkImageProvider(
              'https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/23/bba16129-c477-4094-b0b4-5cf7e892b306.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Center(
        child: Text(
          (controller.pageData['statusDesc'] as String?) ?? '',
          style: TextStyle(
            color: Colors.white,
            fontSize: 15.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildAddressCard(BuildContext context) {
    final addressInfo = controller.pageData['addressInfo'] ?? {};
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 10.sp, left: 8.sp, right: 8.sp),
      padding: EdgeInsets.symmetric(
        vertical: 16.sp,
        horizontal: 8.sp,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            Icons.location_on,
            color: AppColors.primaryColor,
            size: 24.sp,
          ),
          SizedBox(width: 8.sp),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                        (addressInfo as Map<String, dynamic>)['userName'] ?? '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF333333),
                        )),
                    SizedBox(width: 8.sp),
                    Text(
                      addressInfo['phone'] ?? '',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4.sp),
                Text(
                  addressInfo['address'] ?? '',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF666666),
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransportCard(BuildContext context) {
    if ((controller.pageData['transportAmountList'] as List?)?.isNotEmpty !=
        true) {
      return const SizedBox.shrink();
    }
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 10.sp, left: 8.sp, right: 8.sp),
      padding: EdgeInsets.symmetric(
        vertical: 16.sp,
        horizontal: 8.sp,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              controller.showTransportInfoDialog(context);
            },
            behavior: HitTestBehavior.opaque,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    I18n.of(context)?.translate('cm_order.chooseTransport') ??
                        '选择运输方式',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF333333),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 2.sp),
                  child: Icon(
                    Icons.help,
                    size: 20.sp,
                    color: AppColors.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 10.sp),
          ...(controller.pageData['transportAmountList'] as List<dynamic>? ??
                  [])
              .map<Widget>(
                  (transport) => _buildTransportItem(transport, context))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildTransportItem(
      Map<String, dynamic> transport, BuildContext context) {
    final isSelected =
        controller.pageData['transportId'] == transport['transportId'];
    final hasDetails = (transport['amountDetailList'] ?? []).isNotEmpty;

    return GestureDetector(
      onTap: controller.isPayFee.value
          ? () =>
              controller.updateTransport(transport['transportId'].toString())
          : null,
      behavior: HitTestBehavior.opaque,
      child: Container(
        margin: EdgeInsets.only(bottom: 10.sp),
        padding: EdgeInsets.all(12.sp),
        decoration: BoxDecoration(
          border: Border.all(
            color:
                isSelected ? AppColors.primaryColor : const Color(0xFFEEEEEE),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Transform.scale(
                  scale: 0.9,
                  child: Theme(
                    data: ThemeData(
                      disabledColor: isSelected
                          ? AppColors.primaryColor
                          : Colors.grey.shade400,
                      unselectedWidgetColor: Colors.grey.shade400,
                    ),
                    child: Radio<String>(
                      value: transport['transportId']?.toString() ?? '',
                      groupValue:
                          (controller.pageData['transportId'] as dynamic)
                              ?.toString(),
                      onChanged: controller.isPayFee.value
                          ? (value) => controller.updateTransport(value!)
                          : null,
                      fillColor:
                          MaterialStateProperty.resolveWith<Color>((states) {
                        if (states.contains(MaterialState.disabled) &&
                            isSelected) {
                          return AppColors.primaryColor;
                        }
                        if (states.contains(MaterialState.selected)) {
                          return AppColors.primaryColor;
                        }
                        return Colors.grey.shade400;
                      }),
                      visualDensity: VisualDensity.compact,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ),
                Text(
                  transport['name'] ?? '',
                  style: TextStyle(fontSize: 16.sp),
                ),
              ],
            ),
            SizedBox(height: 12.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  I18n.of(context)?.translate('cm_order.feeAmount') ?? '费用金额',
                  style: TextStyle(fontSize: 14.sp),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      setUnit(transport['amount']),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (hasDetails)
                      TextButton(
                        onPressed: () => controller
                            .showFeeDetails(transport['amountDetailList']),
                        style: TextButton.styleFrom(
                          backgroundColor: const Color(0xFFF6D2D4),
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          minimumSize: Size(0, 40.h),
                        ),
                        child: Text(
                          I18n.of(context)
                                  ?.translate('cm_order.feeAmountDetails') ??
                              '费用明细',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFFE50013),
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 8.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  I18n.of(context)?.translate('cm_order.expectDeliveryTime') ??
                      'Tiempo estimado de tránsito:',
                  style: TextStyle(fontSize: 14.sp),
                ),
                Text(
                  transport['expectDeliveryTime'] ?? '',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            if (transport['transportRemark']?.isNotEmpty == true) ...[
              SizedBox(height: 16.h),
              Container(
                width: double.infinity,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      I18n.of(context)
                              ?.translate('cm_order.shippingInformation') ??
                          '物流信息',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF868686),
                      ),
                    ),
                    SizedBox(height: 6.h),
                    Text(
                      transport['transportRemark'],
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF868686),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTransportDetail(
    String label,
    String value, {
    bool showDetails = false,
    VoidCallback? onTapDetails,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: TextStyle(fontSize: 14.sp)),
        Row(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (showDetails) ...[
              SizedBox(width: 4.w),
              TextButton(
                onPressed: onTapDetails,
                child: Text(
                  'Detalles del monto de la tarifa',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildOrderInfoCard(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 10.sp, left: 8.sp, right: 8.sp),
      padding: EdgeInsets.only(
        top: 16.sp,
        left: 4.sp,
        right: 4.sp,
        bottom: 4.sp,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              SizedBox(
                width: 140.sp,
                child: Text(
                  I18n.of(context)?.translate('cm_order.orderNo') ??
                      'Número de orden：',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ),
              Text(
                (controller.pageData['orderNo'] as String?) ?? '',
                style: TextStyle(
                  fontSize: 13.sp,
                ),
              ),
            ],
          ),
          SizedBox(height: 5.sp),
          Row(
            children: [
              SizedBox(
                width: 140.sp,
                child: Text(
                  I18n.of(context)?.translate('cm_order.orderTime') ??
                      'Ttiempo de pedidos：',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ),
              Text(
                timeFormatByZone(controller.pageData['orderTime'] as int? ?? 0),
                style: TextStyle(
                  fontSize: 13.sp,
                ),
              ),
            ],
          ),
          SizedBox(height: 15.sp),
          ..._buildBoxList(),
        ],
      ),
    );
  }

  List<Widget> _buildBoxList() {
    final boxList = controller.pageData['boxList'] as List<dynamic>? ?? [];
    return List.generate(
      boxList.length,
      (boxIndex) => _buildBoxItem(
          boxList[boxIndex] as Map<String, dynamic>?, boxIndex, Get.context!),
    );
  }

  Widget _buildBoxItem(
      Map<String, dynamic>? box, int boxIndex, BuildContext context) {
    if (box == null) return const SizedBox();

    return Container(
      margin: EdgeInsets.only(bottom: 12.sp),
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(
            color: const Color(0xFFFF6B81),
            width: 4.sp,
          ),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.only(left: 8.sp),
        child: Column(
          children: [
            ...List.generate(
              (box['skuList'] as List?)?.length ?? 0,
              (index) => _buildSkuItem(
                  (box['skuList'] as List?)?[index] as Map<String, dynamic>?,
                  context),
            ),
            SizedBox(height: 16.h),
            Container(
              padding: EdgeInsets.all(12.sp),
              color: const Color(0xFFFAFAFA),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        I18n.of(context)?.translate('cm_order.boxCount') ??
                            'Bultos',
                        style: TextStyle(fontSize: 13.sp),
                      ),
                      Text(
                        '${box['boxCount']}',
                        style: TextStyle(fontSize: 13.sp),
                      ),
                    ],
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 5.sp),
                    child: DottedBorder(
                      color: const Color(0xFFCCCCCC),
                      strokeWidth: 1,
                      dashPattern: [2, 2],
                      padding: EdgeInsets.symmetric(vertical: 1.sp),
                      child: Container(width: double.infinity),
                      customPath: (size) {
                        Path path = Path();
                        path.moveTo(0, size.height);
                        path.lineTo(size.width, size.height);
                        return path;
                      },
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        I18n.of(context)?.translate('cm_order.boxSkuCount') ??
                            'Unidades/Bulto',
                        style: TextStyle(fontSize: 13.sp),
                      ),
                      Text(
                        '${box['skuCount']}',
                        style: TextStyle(fontSize: 13.sp),
                      ),
                    ],
                  ),
                  if (!isEmptyObject(controller.pageData['currentTransport']) &&
                      controller.pageData['transportAmountType'] == 1) ...[
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 5.sp),
                      child: DottedBorder(
                        color: const Color(0xFFCCCCCC),
                        strokeWidth: 1,
                        dashPattern: [2, 2],
                        padding: EdgeInsets.symmetric(vertical: 1.sp),
                        child: Container(width: double.infinity),
                        customPath: (size) {
                          Path path = Path();
                          path.moveTo(0, size.height);
                          path.lineTo(size.width, size.height);
                          return path;
                        },
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          I18n.of(context)?.translate('cm_order.boxAmount') ??
                              'Costo de envío por uno bulto',
                          style: TextStyle(fontSize: 13.sp),
                        ),
                        Text(
                          setUnit(box['boxFee']),
                          style: TextStyle(fontSize: 13.sp),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkuItem(Map<String, dynamic>? sku, BuildContext context) {
    if (sku == null) return const SizedBox();
    return Container(
      margin: EdgeInsets.only(bottom: 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(6.r),
            child: MyCachedNetworkImage(
              imageUrl: sku['picUrl'] ?? '',
              width: 62.sp,
              height: 62.sp,
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(width: 6.sp),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () => Get.toNamed(
                    '/goods/${sku['goodsId']}',
                    arguments: {'goodsId': sku['goodsId']},
                  ),
                  child: Text(
                    sku['goodsName'] ?? '',
                    style: TextStyle(
                      fontSize: 13.sp,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF333333),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  controller.getSkuName(sku['specList'] ?? []),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF7F7F7F),
                  ),
                ),
                Text(
                  '${I18n.of(context)?.translate('cm_order.skuNo')}：${sku['skuNo']}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    height: 20.sp / 13.sp,
                    color: const Color(0xFF7F7F7F),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'x ${sku['count']} ${sku['goodsPriceUnitName'] ?? ''}',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: const Color(0xFF000000),
                        height: 20.sp / 13.sp,
                      ),
                    ),
                    Text(
                      setUnit(sku['unitPrice']),
                      style: TextStyle(
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w500,
                        height: 20.sp / 13.sp,
                      ),
                    ),
                  ],
                ),
                if (!isEmptyObject(controller.pageData['currentTransport']) &&
                    controller.pageData['transportAmountType'] == 1) ...[
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Text(
                              I18n.of(context)?.translate(
                                      'cm_order.receivedUnitPrice') ??
                                  '到手单价',
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: AppColors.primaryColor,
                              ),
                            ),
                            SizedBox(width: 2.sp),
                            GestureDetector(
                              onTap: () {
                                final tip = I18n.of(context)?.translate(
                                        'cm_order.receivedUnitPriceTip') ??
                                    '';
                                showDialog(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    content: Text(tip),
                                  ),
                                );
                              },
                              child: Icon(
                                Icons.help_outline,
                                size: 14.sp,
                                color: AppColors.primaryColor,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          setUnit(num.tryParse(
                                  sku['receivedUnitPrice']?.toString() ?? '') ??
                              0),
                          style: TextStyle(
                            fontSize: 13.sp,
                            fontWeight: FontWeight.w500,
                            color: AppColors.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderFeeCard(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 10.sp, left: 8.sp, right: 8.sp),
      padding: EdgeInsets.symmetric(
        vertical: 16.sp,
        horizontal: 8.sp,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            I18n.of(context)?.translate('cm_order.orderFeeDetails') ??
                'Detalle del coste del pedido',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              height: 1,
            ),
          ),
          SizedBox(height: 10.sp),
          // 产品成本
          Container(
            padding: EdgeInsets.symmetric(vertical: 8.sp),
            margin: EdgeInsets.only(bottom: 4.sp),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Color(0xFFD7D7D7)),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  I18n.of(context)?.translate('cm_order.productCost') ??
                      'Coste del producto',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    height: 1,
                    color: Color(0xFF333333),
                  ),
                ),
                if ((controller.pageData['productAmount']
                        as Map<String, dynamic>?)?['amount'] !=
                    null)
                  Text(
                    setUnit((controller.pageData['productAmount']
                        as Map<String, dynamic>?)?['amount']),
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      height: 1,
                    ),
                  ),
              ],
            ),
          ),
          // 费用列表
          ...((controller.pageData['productAmount']
                      as Map<String, dynamic>?)?['feeList'] as List? ??
                  [])
              .map((fee) => Container(
                    padding: EdgeInsets.symmetric(vertical: 3.sp),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 6,
                          child: Text(
                            '${fee['feeName']}:',
                            style: TextStyle(
                              fontSize: 13.sp,
                              color: Color(0xFF333333),
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            if ((fee['childFeeList']?.length ?? 0) > 0)
                              GestureDetector(
                                onTap: () => controller
                                    .showFeeDetails(fee['childFeeList']),
                                child: Container(
                                  height: 20.sp,
                                  margin: EdgeInsets.only(right: 8.sp),
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 6.sp),
                                  decoration: BoxDecoration(
                                    color: Color(0xFFF6D2D4),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Center(
                                    child: Text(
                                      I18n.of(context)?.translate(
                                              'cm_order.feeAmountDetails') ??
                                          '',
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: Color(0xFFE50013),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            Text(
                              setUnit(fee['feeAmount']),
                              style: TextStyle(
                                fontSize: 13.sp,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ))
              .toList(),
          // 国际费用
          if (controller.pageData['currentTransport'] != null)
            Container(
              margin: EdgeInsets.only(top: 4.sp),
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 8.sp),
                    margin: EdgeInsets.only(bottom: 4.sp),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(color: Color(0xFFD7D7D7)),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${I18n.of(context)?.translate("cm_order.interFees")}：',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            height: 1,
                            color: Color(0xFF333333),
                          ),
                        ),
                        if ((controller.pageData['currentTransport']
                                as Map<String, dynamic>?)?['amount'] !=
                            null)
                          Text(
                            setUnit((controller.pageData['currentTransport']
                                as Map<String, dynamic>?)?['amount']),
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                              height: 1,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 3.sp),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                            I18n.of(context)?.translate('cm_order.feeAmount') ??
                                '',
                            style: TextStyle(
                              fontSize: 13.sp,
                              color: Color(0xFF333333),
                            )),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                                setUnit((controller.pageData['currentTransport']
                                    as Map<String, dynamic>?)?['amount']),
                                style: TextStyle(
                                  fontSize: 13.sp,
                                )),
                            if (((controller.pageData['currentTransport']
                                                as Map<String, dynamic>?)?[
                                            'amountDetailList']
                                        ?.length ??
                                    0) >
                                0)
                              GestureDetector(
                                onTap: () => controller.showFeeDetails(
                                    (controller.pageData['currentTransport']
                                            as Map<String, dynamic>?)?[
                                        'amountDetailList']),
                                child: Container(
                                  height: 40,
                                  padding: EdgeInsets.symmetric(horizontal: 12),
                                  decoration: BoxDecoration(
                                    color: Color(0xFFF6D2D4),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Center(
                                    child: Text(
                                      I18n.of(context)?.translate(
                                              'cm_order.feeAmountDetails') ??
                                          '',
                                      style: TextStyle(
                                        fontSize: 24,
                                        color: Color(0xFFE50013),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPaymentCard(BuildContext context) {
    if (!controller.isPayFee.value) {
      return const SizedBox.shrink();
    }
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 10.sp, left: 8.sp, right: 8.sp),
      padding: EdgeInsets.only(
        top: 16.sp,
        left: 8.sp,
        right: 8.sp,
        bottom: 4.sp,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (controller.isOfflinePayment ||
              controller.isPayInterFee.value) ...[
            if (controller.isPayAllFee.value ||
                controller.isPayProduct.value) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    I18n.of(context)?.translate('cm_order.productQuantity') ??
                        '',
                    style: TextStyle(fontSize: 14.sp),
                  ),
                  Text(
                    (controller.pageData['totalCount'] ?? 0).toString(),
                    style: TextStyle(fontSize: 14.sp),
                  ),
                ],
              ),
              Divider(height: 20.sp, color: const Color(0xFFEEEEEE)),
            ],
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  I18n.of(context)?.translate('cm_order.amountToPay') ?? '',
                  style: TextStyle(fontSize: 14.sp),
                ),
                Text(
                  setUnit(num.tryParse(controller
                              .pageData['actualPaymentAmount']
                              ?.toString() ??
                          '0') ??
                      0),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.primaryColor,
                  ),
                ),
              ],
            ),
            if (controller.pageData['paymentAmountMessage'] != null)
              Row(
                children: [
                  Icon(
                    Icons.info_outlined,
                    size: 14.sp,
                    color: const Color(0xFF999999),
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    (controller.pageData['paymentAmountMessage'] as String)
                        .toLowerCase(),
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
                ],
              ),
            SizedBox(height: 10.sp)
          ] else ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  I18n.of(context)?.translate('cm_order.totalPaymentAmount') ??
                      '',
                  style: TextStyle(fontSize: 14.sp),
                ),
                Text(
                  setUnit(num.tryParse(
                          controller.pageData['paymentAmount']?.toString() ??
                              '0') ??
                      0),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            if (controller.pageData['paymentAmountMessage'] != null)
              Row(
                children: [
                  Icon(
                    Icons.info_outlined,
                    size: 14.sp,
                    color: const Color(0xFF999999),
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    (controller.pageData['paymentAmountMessage'] as String)
                        .toLowerCase(),
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
                ],
              ),
            SizedBox(height: 14.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  I18n.of(context)?.translate('cm_order.coupon') ?? '',
                  style: TextStyle(fontSize: 14.sp),
                ),
                Text(
                  setUnit(num.tryParse(
                          controller.pageData['discountedAmount']?.toString() ??
                              '0') ??
                      0),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.primaryColor,
                  ),
                ),
              ],
            ),
            Column(
              children: [
                // 产品券
                Container(
                  margin: EdgeInsets.only(top: 12.sp, bottom: 6.sp),
                  child: ((controller.pageData['couponInfo'] as Map<String,
                                  dynamic>?)?['productCouponList'] as List?)
                              ?.isNotEmpty ??
                          false
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  I18n.of(context)?.translate(
                                          'cm_coupon.productCoupons') ??
                                      '',
                                  style: TextStyle(fontSize: 14.sp, height: 1),
                                ),
                                GestureDetector(
                                  onTap: () => controller
                                      .onChooseCoupon('COUPON_TYPE_PRODUCT'),
                                  child: Container(
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          (controller.pageData['couponInfo']
                                                          as Map<String,
                                                              dynamic>?)?[
                                                      'productCouponAmount'] !=
                                                  null
                                              ? setUnit((controller.pageData[
                                                          'couponInfo']
                                                      as Map<String,
                                                          dynamic>?)?[
                                                  'productCouponAmount'])
                                              : '${I18n.of(context)?.translate('cm_order.youHaveCoupon') ?? ''} ${(controller.pageData['couponInfo'] as Map<String, dynamic>?)?['productCouponList']?.length ?? 0} ${I18n.of(context)?.translate('cm_order.availableCoupons') ?? ''}',
                                          style: TextStyle(
                                            fontSize: (controller.pageData[
                                                                'couponInfo']
                                                            as Map<String,
                                                                dynamic>?)?[
                                                        'productCouponAmount'] !=
                                                    null
                                                ? 14.sp
                                                : 12.sp,
                                            color: AppColors.primaryColor,
                                          ),
                                        ),
                                        Icon(
                                          size: 15.sp,
                                          Icons.arrow_forward_ios,
                                          color: Color(0xFF999999),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        )
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  I18n.of(context)?.translate(
                                          'cm_coupon.productCoupons') ??
                                      '',
                                  style: TextStyle(fontSize: 14.sp, height: 1),
                                ),
                                Container(
                                  child: Row(
                                    children: [
                                      Text(
                                        I18n.of(context)?.translate(
                                                'cm_order.noAvailableCoupons') ??
                                            '',
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: const Color(0xFF7F7F7F),
                                        ),
                                      ),
                                      Icon(
                                        size: 15.sp,
                                        Icons.arrow_forward_ios,
                                        color: Color(0xFF999999),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                ),
                // 佣金券
                Container(
                  margin: EdgeInsets.only(bottom: 16.sp),
                  child: ((controller.pageData['couponInfo'] as Map<String,
                                  dynamic>?)?['commissionCouponList'] as List?)
                              ?.isNotEmpty ??
                          false
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  I18n.of(context)?.translate(
                                          'cm_coupon.commissionCoupons') ??
                                      '',
                                  style: TextStyle(fontSize: 14.sp, height: 1),
                                ),
                                GestureDetector(
                                  onTap: () => controller
                                      .onChooseCoupon('COUPON_TYPE_COMMISSION'),
                                  child: Container(
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          (controller.pageData['couponInfo']
                                                          as Map<String,
                                                              dynamic>?)?[
                                                      'commissionCouponAmount'] !=
                                                  null
                                              ? setUnit((controller.pageData[
                                                          'couponInfo']
                                                      as Map<String,
                                                          dynamic>?)?[
                                                  'commissionCouponAmount'])
                                              : '${I18n.of(context)?.translate('cm_order.youHaveCoupon') ?? ''} ${(controller.pageData['couponInfo'] as Map<String, dynamic>?)?['commissionCouponList']?.length ?? 0} ${I18n.of(context)?.translate('cm_order.availableCoupons') ?? ''}',
                                          style: TextStyle(
                                            fontSize: (controller.pageData[
                                                                'couponInfo']
                                                            as Map<String,
                                                                dynamic>?)?[
                                                        'commissionCouponAmount'] !=
                                                    null
                                                ? 14.sp
                                                : 12.sp,
                                            color: AppColors.primaryColor,
                                          ),
                                        ),
                                        Icon(
                                          size: 15.sp,
                                          Icons.arrow_forward_ios,
                                          color: Color(0xFF999999),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        )
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  I18n.of(context)?.translate(
                                          'cm_coupon.commissionCoupons') ??
                                      '',
                                  style: TextStyle(fontSize: 14.sp),
                                ),
                                Container(
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        I18n.of(context)?.translate(
                                                'cm_order.noAvailableCoupons') ??
                                            '',
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: const Color(0xFF7F7F7F),
                                        ),
                                      ),
                                      Icon(
                                        size: 15.sp,
                                        Icons.arrow_forward_ios,
                                        color: Color(0xFF999999),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                ),
              ],
            ),
            Divider(height: 1.sp, color: Color(0xFFEEEEEE)),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 12.sp),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    I18n.of(context)?.translate('cm_order.productQuantity') ??
                        '',
                    style: TextStyle(fontSize: 14.sp),
                  ),
                  Text(
                    (controller.pageData['totalCount'] ?? 0).toString(),
                    style: TextStyle(fontSize: 14.sp),
                  ),
                ],
              ),
            ),
            Divider(height: 1.sp, color: Color(0xFFEEEEEE)),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 12.sp),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    I18n.of(context)?.translate('cm_order.amountToPay') ?? '',
                    style: TextStyle(fontSize: 16.sp),
                  ),
                  Text(
                    setUnit(num.tryParse(controller
                                .pageData['actualPaymentAmount']
                                ?.toString() ??
                            '0') ??
                        0),
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMerchantRemarkCard(BuildContext context) {
    // 如果没有商家备注，则不显示
    if (controller.pageData['merChantRemark'] == null ||
        controller.pageData['merChantRemark'].toString().isEmpty) {
      return const SizedBox.shrink();
    }
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 10.sp, left: 8.sp, right: 8.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: 16.sp,
          horizontal: 8.sp,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              I18n.of(context)?.translate('cm_order.merchantRemark') ?? '',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8.sp),
            Container(
              height: 104.sp,
              width: double.infinity,
              padding: EdgeInsets.all(12.sp),
              decoration: BoxDecoration(
                border: Border.all(
                    color: const Color.fromARGB(255, 102, 102, 102),
                    width: 1.sp),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Text(
                (controller.pageData['merChantRemark'] as String?) ?? '',
                style: TextStyle(fontSize: 14.sp),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerRemarkCard(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 10.sp, left: 8.sp, right: 8.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: 16.sp,
          horizontal: 8.sp,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              I18n.of(context)?.translate('cm_order.customerRemark') ?? '',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8.sp),
            Builder(
              builder: (context) {
                if ((controller.pageData['orderRemark'] != null &&
                        controller.pageData['orderRemark']
                            .toString()
                            .isNotEmpty) &&
                    controller.remarkController.text.isEmpty) {
                  controller.remarkController.text =
                      controller.pageData['orderRemark'].toString();
                }
                return TextField(
                  controller: controller.remarkController,
                  maxLines: 3,
                  enabled: controller.isPayFee.value,
                  decoration: InputDecoration(
                    hintText: controller.isPayFee.value
                        ? I18n.of(context)
                            ?.translate('cm_order.orderRemarkInput')
                        : null,
                    hintStyle: TextStyle(
                      fontSize: 13.sp,
                      color: const Color(0xFF999999),
                    ),
                    border: OutlineInputBorder(), // 默认边框（必要）
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: const Color.fromARGB(255, 102, 102, 102),
                        width: 1.0,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: const Color.fromARGB(255, 102, 102, 102),
                        width: 1.0,
                      ),
                    ),
                    disabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: const Color.fromARGB(255, 102, 102, 102),
                        width: 1.0,
                      ),
                    ),
                  ),
                  onChanged: controller.updateOrderRemark,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomPaymentBar(BuildContext context) {
    if (!controller.isPayFee.value) {
      return const SizedBox.shrink();
    }
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          left: 8.sp,
          right: 8.sp,
          bottom: 8.sp,
          top: 16.sp,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 10.sp,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  setUnit(
                      (controller.pageData['actualPaymentAmount'] as num?) ??
                          0),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Row(
                  children: [
                    ElevatedButton(
                      onPressed: () => controller.showCancelDrawer(context),
                      style: ElevatedButton.styleFrom(
                        minimumSize: Size(0, 30.sp),
                        backgroundColor: const Color(0xFFF6D2D4),
                        padding: EdgeInsets.symmetric(horizontal: 14.sp),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: Text(
                        I18n.of(context)?.translate('cm_order.orderCancel') ??
                            'Cancelar',
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: AppColors.primaryColor,
                        ),
                      ),
                    ),
                    SizedBox(width: 5.sp),
                    ElevatedButton(
                      onPressed: controller.isOfflinePayment
                          ? null
                          : () => controller.onOrderPay(),
                      style: ElevatedButton.styleFrom(
                        minimumSize: Size(0, 30.sp),
                        backgroundColor: controller.isOfflinePayment
                            ? const Color(0xFFE6E6E6)
                            : AppColors.primaryColor,
                        padding: EdgeInsets.symmetric(horizontal: 14.sp),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: Text(
                        I18n.of(context)?.translate('cm_order.orderPay') ??
                            'Pago',
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: controller.isOfflinePayment
                              ? Colors.black
                              : Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (controller.isOfflinePayment) ...[
              SizedBox(height: 4.sp),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16.sp,
                    color: AppColors.primaryColor,
                  ),
                  SizedBox(width: 3.sp),
                  Text(
                    I18n.of(context)?.translate('cm_order.orderPayTip') ?? '',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ],
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Transform.scale(
                  scale: 0.8,
                  child: Checkbox(
                    value: controller.acceptTerms,
                    onChanged: (value) =>
                        controller.updateAcceptTerms(value ?? false),
                    activeColor: AppColors.primaryColor,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    visualDensity: VisualDensity.compact,
                  ),
                ),
                Text(
                  I18n.of(context)?.translate('cm_order.readAgree') ?? '',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
                SizedBox(width: 4.sp),
                GestureDetector(
                  onTap: () => controller.onOpenAgreeModal(context),
                  child: Text(
                    I18n.of(context)?.translate('cm_news.termsOfService') ?? '',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF206CCF),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBillFeeCard(BuildContext context) {
    if (!(controller.pageData['mallOrderStatus'] != null &&
        controller.pageData['mallOrderStatus'] != 'MALL_WAITING_APPROVING' &&
        !controller.isOrderCanceled.value &&
        !controller.isPayAllFee.value &&
        !controller.isPayProduct.value)) {
      return const SizedBox.shrink();
    }

    final currentTransport =
        controller.pageData['currentTransport'] as Map<String, dynamic>?;
    final productCouponAmount =
        controller.pageData['productCouponAmount'] as Map<String, dynamic>?;
    final comissCouponAmount =
        controller.pageData['comissCouponAmount'] as Map<String, dynamic>?;

    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 10.sp, left: 8.sp, right: 8.sp),
      padding: EdgeInsets.symmetric(
        vertical: 16.sp,
        horizontal: 8.sp,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            I18n.of(context)?.translate('cm_order.paidFeeDetails') ?? '',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 10.sp),
          // 产品成本明细（已支付部分）
          if (controller.pageData['productAmount'] != null &&
              (controller.pageData['productAmount']
                      as Map<String, dynamic>?)?['amount'] !=
                  null &&
              (controller.pageData['productAmount']
                      as Map<String, dynamic>?)?['amount'] >
                  0) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  I18n.of(context)?.translate('cm_order.productCost') ?? '',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF333333),
                  ),
                ),
                Text(
                  setUnit(num.tryParse(((controller.pageData['productAmount']
                                  as Map<String, dynamic>?)?['amount']
                              ?.toString()) ??
                          '0') ??
                      0),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            Divider(height: 10.sp, color: const Color(0xFFD7D7D7)),
            ...?(((controller.pageData['productAmount']
                    as Map<String, dynamic>?)?['feeList'] as List?)
                ?.map((fee) => Padding(
                      padding: EdgeInsets.symmetric(vertical: 3.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            flex: 6,
                            child: Text(
                              '${fee['feeName']}:',
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: Color(0xFF333333),
                              ),
                            ),
                          ),
                          Row(
                            children: [
                              if ((fee['childFeeList'] as List?)?.isNotEmpty ??
                                  false)
                                GestureDetector(
                                  onTap: () => controller
                                      .showFeeDetails(fee['childFeeList']),
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 8.w,
                                      vertical: 4.h,
                                    ),
                                    margin: EdgeInsets.only(right: 8.sp),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFF6D2D4),
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                    child: Text(
                                      I18n.of(context)?.translate(
                                              'cm_order.feeAmountDetails') ??
                                          '',
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: const Color(0xFFE50013),
                                      ),
                                    ),
                                  ),
                                ),
                              Text(
                                setUnit(num.tryParse(
                                        fee['feeAmount']?.toString() ?? '0') ??
                                    0),
                                style: TextStyle(fontSize: 13.sp),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ))
                ?.toList()),
          ],
          // 国际费用明细（已支付部分）
          if (!isEmptyObject(currentTransport) &&
              !controller.hideInterFeeDisplay &&
              (currentTransport?['amount'] != null &&
                  currentTransport?['amount'] > 0)) ...[
            SizedBox(height: 10.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${I18n.of(context)?.translate('cm_order.interFees') ?? ''}:',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF333333),
                  ),
                ),
                Text(
                  setUnit(num.tryParse(
                          currentTransport?['amount']?.toString() ?? '0') ??
                      0),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            Divider(height: 10.sp, color: const Color(0xFFD7D7D7)),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    I18n.of(context)?.translate('cm_order.feeAmount') ?? '',
                    style: TextStyle(fontSize: 13.sp),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        setUnit(num.tryParse(
                                currentTransport?['amount']?.toString() ??
                                    '0') ??
                            0),
                        style: TextStyle(fontSize: 13.sp),
                      ),
                      if ((currentTransport?['amountDetailList'] as List?)
                              ?.isNotEmpty ??
                          false)
                        GestureDetector(
                          onTap: () => controller.showFeeDetails(
                              currentTransport?['amountDetailList']),
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            margin: EdgeInsets.only(top: 4.h),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF6D2D4),
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            child: Text(
                              I18n.of(context)?.translate(
                                      'cm_order.feeAmountDetails') ??
                                  '',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: const Color(0xFFE50013),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
          if (controller.pageData['productAddComissSumAmount'] != null &&
              num.tryParse(controller.pageData['productAddComissSumAmount']
                          ?.toString() ??
                      '0') !=
                  0 &&
              !controller.isOfflinePayment) ...[
            SizedBox(height: 10.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${I18n.of(context)?.translate('cm_order.coupon') ?? ''}:',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF333333),
                  ),
                ),
                Text(
                  setUnit(num.tryParse(controller
                              .pageData['productAddComissSumAmount']
                              ?.toString() ??
                          '0') ??
                      0),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            Divider(height: 10.sp, color: const Color(0xFFD7D7D7)),
            if ((productCouponAmount?['amount']) != null)
              CouponDetail(
                title:
                    I18n.of(context)?.translate('cm_coupon.productCoupons') ??
                        '',
                amount: productCouponAmount?['amount'],
                couponType: 'COUPON_TYPE_PRODUCT',
                couponList: productCouponAmount?['productCouponList'],
              ),
            if ((comissCouponAmount?['amount']) != null)
              Padding(
                padding: EdgeInsets.only(top: 8.h),
                child: CouponDetail(
                  title: I18n.of(context)
                          ?.translate('cm_coupon.commissionCoupons') ??
                      '',
                  amount: comissCouponAmount?['amount'],
                  couponType: 'COUPON_TYPE_COMMISSION',
                  couponList: comissCouponAmount?['commissionCouponList'],
                ),
              ),
          ],
          SizedBox(height: 10.sp),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${I18n.of(context)?.translate('cm_order.actualPaymentTotal') ?? ''}:',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF333333),
                ),
              ),
              Text(
                setUnit(num.tryParse(
                        controller.pageData['paidAmount']?.toString() ?? '0') ??
                    0),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

bool isEmptyObject(dynamic obj) {
  if (obj == null) return true;
  if (obj is Map) return obj.isEmpty;
  if (obj is List) return obj.isEmpty;
  if (obj is String) return obj.isEmpty;
  return false;
}
