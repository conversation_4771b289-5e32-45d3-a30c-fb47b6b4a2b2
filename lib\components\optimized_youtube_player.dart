import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';

/// 优化的YouTube播放器包装器，专门处理Android渲染问题
class OptimizedYouTubePlayer extends StatefulWidget {
  final YoutubePlayerController controller;
  final double aspectRatio;

  const OptimizedYouTubePlayer({
    Key? key,
    required this.controller,
    this.aspectRatio = 16 / 9,
  }) : super(key: key);

  @override
  State<OptimizedYouTubePlayer> createState() => _OptimizedYouTubePlayerState();
}

class _OptimizedYouTubePlayerState extends State<OptimizedYouTubePlayer>
    with WidgetsBindingObserver {
  bool _isVisible = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    // 监听系统UI变化，减少渲染冲突
    _setupSystemUIOptimization();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  /// 设置系统UI优化
  void _setupSystemUIOptimization() {
    // 设置系统UI覆盖，减少渲染冲突
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: Colors.transparent,
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    // 当应用状态改变时，暂停/恢复播放器以减少渲染问题
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        setState(() {
          _isVisible = false;
        });
        break;
      case AppLifecycleState.resumed:
        // 延迟恢复，确保系统渲染稳定
        Future.delayed(Duration(milliseconds: 300), () {
          if (mounted) {
            setState(() {
              _isVisible = true;
            });
          }
        });
        break;
      case AppLifecycleState.detached:
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) {
      return _buildPlaceholder();
    }

    if (_hasError) {
      return _buildErrorWidget();
    }

    return RepaintBoundary(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(12),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: AspectRatio(
            aspectRatio: widget.aspectRatio,
            child: YoutubePlayer(
              controller: widget.controller,
              aspectRatio: widget.aspectRatio,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建占位符
  Widget _buildPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
      ),
      child: AspectRatio(
        aspectRatio: widget.aspectRatio,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.play_circle_outline,
                size: 64,
                color: Colors.white.withOpacity(0.7),
              ),
              SizedBox(height: 16),
              Text(
                '视频播放器已暂停',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
      ),
      child: AspectRatio(
        aspectRatio: widget.aspectRatio,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red.withOpacity(0.7),
              ),
              SizedBox(height: 16),
              Text(
                '视频加载失败',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 16,
                ),
              ),
              SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _hasError = false;
                  });
                },
                child: Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 渲染优化工具类
class YouTubeRenderingOptimizer {
  /// 应用渲染优化设置
  static void applyOptimizations() {
    // 设置系统UI模式
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  /// 清理渲染优化设置
  static void clearOptimizations() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// 检查是否为已知的渲染问题设备
  static bool isProblematicDevice() {
    // 这里可以添加已知有渲染问题的设备检测
    // 例如某些小米设备、华为设备等
    return false; // 暂时返回false，可以根据需要扩展
  }
}
