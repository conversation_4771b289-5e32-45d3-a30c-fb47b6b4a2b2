import 'package:flutter/material.dart';
import 'package:chilat2_mall_app/components/search_header.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/components/my_footer.dart';
import 'package:chilat2_mall_app/components/app_scaffold.dart';
import 'package:chilat2_mall_app/components/webview_youtube_modal.dart';

class PaymentMethodsPage extends StatefulWidget {
  const PaymentMethodsPage({Key? key}) : super(key: key);

  @override
  State<PaymentMethodsPage> createState() => _PaymentMethodsPageState();
}

class _PaymentMethodsPageState extends State<PaymentMethodsPage> {
  // YouTube视频ID
  final String videoId = "-5ZBcr0rgJs";

  // 打开YouTube视频 - 使用模态框播放
  Future<void> _openYouTubeVideo() async {
    // 验证视频ID格式
    if (!WebViewYouTubeHelper.isValidVideoId(videoId)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('无效的视频ID')),
        );
      }
      return;
    }

    try {
      // 显示YouTube视频模态框
      await WebViewYouTubeHelper.showVideoModal(
        context,
        videoId: videoId,
        title: '支付方式教程',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('播放视频时出错: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    return AppScaffold(
      showScrollToTopButton: true,
      scrollController: scrollController,
      body: SafeArea(
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SearchHeader(
                showBackIcon: true,
              ),
              Padding(
                padding: EdgeInsets.only(
                    top: 42.sp, bottom: 20.sp, left: 16.sp, right: 16.sp),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'Formas de pago',
                      style: TextStyle(
                        fontSize: 34.sp,
                        fontWeight: FontWeight.w500,
                        height: 1.0,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 52.sp),
                    // 第一部分
                    _buildSection(
                      title: '1. ¿Cuáles son las modalidades de pago?',
                      description:
                          'Chilatshop ofrece diferentes opciones en cuanto a métodos de pago. No '
                          'sólo admite los principales métodos de pago internacionales, sino que '
                          'también ofrece una amplia gama de opciones de pago en moneda local. '
                          'Esto significa que puede realizar transacciones utilizando métodos de '
                          'pago conocidos sin gastos de conversión adicionales. Esta flexibilidad '
                          'en los métodos de pago hace que sus transacciones transfronterizas '
                          'sean más cómodas.',
                      child: Column(
                        children: [
                          SizedBox(height: 46.sp),
                          _buildPaymentIcons(),
                          SizedBox(height: 52.sp),
                          _buildPaymentMethods(),
                        ],
                      ),
                    ),

                    // 第二部分
                    _buildSection(
                      title: '2. ¿Cómo se paga?',
                      description:
                          'Chilatshop admite el pago en línea, cuando envíe una consulta y '
                          'confirme el pedido, se generará un enlace de pago en su centro '
                          'personal, puede pagar directamente haciendo clic en el enlace, no es '
                          'necesario ir al banco para transferir dinero, es muy cómodo y rápido.',
                      child: Column(
                        children: [
                          SizedBox(height: 36.sp),
                          Image.asset(
                            'assets/images/article/mobilePayOrder.png',
                            width: double.infinity,
                            fit: BoxFit.contain,
                          ),
                        ],
                      ),
                    ),

                    // 第三部分
                    _buildSection(
                      title:
                          '3. Países en los que actualmente es posible pagar en moneda local',
                      description: '',
                      child: Column(
                        children: [
                          SizedBox(height: 36.sp),
                          GridView.count(
                            crossAxisCount: 2,
                            crossAxisSpacing: 20.sp,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            children: _buildCountryGridItems(),
                          ),
                        ],
                      ),
                    ),

                    // 第四部分
                    _buildSection(
                      title: '4. Pagar por tutoriales',
                      description: '',
                      child: Container(
                        margin: EdgeInsets.only(top: 40.sp),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(10.sp),
                          child: InkWell(
                            onTap: _openYouTubeVideo,
                            child: Stack(
                              children: [
                                // 视频缩略图
                                Image.network(
                                  'https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/01/02/f18bcaa1-de0b-4a0b-bcfe-f1832a229174.png',
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: 200.sp,
                                ),
                                // 播放按钮覆盖层
                                Positioned.fill(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.3),
                                      borderRadius:
                                          BorderRadius.circular(10.sp),
                                    ),
                                    child: Center(
                                      child: Container(
                                        width: 60.sp,
                                        height: 60.sp,
                                        decoration: BoxDecoration(
                                          color: Colors.red,
                                          shape: BoxShape.circle,
                                          boxShadow: [
                                            BoxShadow(
                                              color:
                                                  Colors.black.withOpacity(0.3),
                                              blurRadius: 8,
                                              offset: Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: Icon(
                                          Icons.play_arrow,
                                          color: Colors.white,
                                          size: 36.sp,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 底部信息栏
              const MyFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String description,
    required Widget child,
  }) {
    return Column(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 24.sp,
            fontWeight: FontWeight.w500,
            height: 1.17,
          ),
          textAlign: TextAlign.center,
        ),
        if (description.isNotEmpty) ...[
          SizedBox(height: 16.sp),
          Text(
            description,
            style: TextStyle(
              fontSize: 14.sp,
              height: 1.14,
              color: const Color(0xFF7F7F7F),
            ),
            textAlign: TextAlign.center,
          ),
        ],
        SizedBox(height: 30.sp),
        _buildDivider(),
        child,
        SizedBox(height: 80.sp),
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      width: 50.0,
      height: 3.0,
      color: const Color(0xFFE50113),
    );
  }

  Widget _buildPaymentIcons() {
    return Wrap(
      spacing: 12.0,
      runSpacing: 12.0,
      alignment: WrapAlignment.center,
      children: [
        Image.asset('assets/images/article/Visa.png', height: 46.0),
        Image.asset('assets/images/article/BBVA.png', height: 44.0),
        Image.asset('assets/images/article/Interbank.png', height: 44.0),
        Image.asset('assets/images/article/Scotiabank.png', height: 44.0),
        Image.asset('assets/images/article/Paypal.png', height: 40.0),
        Image.asset('assets/images/article/Yape.png', height: 44.0),
        Image.asset('assets/images/article/Plin.png', height: 44.0),
        Image.asset('assets/images/article/Tunki.png', height: 44.0),
      ],
    );
  }

  Widget _buildPaymentMethods() {
    return Wrap(
      spacing: 10.0,
      runSpacing: 10.0,
      alignment: WrapAlignment.center,
      children: [
        _buildPaymentMethodBox(
          icon: 'assets/images/article/transfer.svg',
          title: 'Deposite /\nTransferencia bancaria',
          description: '',
        ),
        _buildPaymentMethodBox(
          icon: 'assets/images/article/cash.svg',
          title: 'Pago en efectivo',
          description: '',
        ),
        _buildPaymentMethodBox(
          icon: 'assets/images/article/ewallet.svg',
          title: 'Billetera\nelectrónica',
          description: 'Tupay (including Yape, PLlN, Tunki, etc.)',
        ),
        _buildPaymentMethodBox(
          icon: 'assets/images/article/others.svg',
          title: 'Otros métodos de pago',
          description: 'Paypal, pago fuera de línea en USD',
        ),
      ],
    );
  }

  Widget _buildPaymentMethodBox({
    required String icon,
    required String title,
    required String description,
  }) {
    return Container(
      width: 166.sp,
      height: 168.sp,
      padding: EdgeInsets.fromLTRB(12.sp, 14.sp, 12.sp, 14.sp),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(20.sp),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(icon, width: 40.sp, height: 40.sp),
          SizedBox(height: 8.sp),
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              height: 1.125,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          if (description.isNotEmpty) ...[
            SizedBox(height: 8.sp),
            Text(
              description,
              style: TextStyle(
                fontSize: 13.sp,
                height: 1.14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildCountryGridItems() {
    final List<Map<String, String>> payMethods = [
      {
        'icon': 'assets/images/article/Mexico.png',
        'country': 'México',
        'currency': 'Peso Mexicano',
      },
      {
        'icon': 'assets/images/article/Peru.png',
        'country': 'Perú',
        'currency': 'Nuevo Sol',
      },
      {
        'icon': 'assets/images/article/Argentina.png',
        'country': 'Argentina',
        'currency': 'Peso argentino',
      },
      {
        'icon': 'assets/images/article/Chile.png',
        'country': 'Chile',
        'currency': 'Peso chileno',
      },
      {
        'icon': 'assets/images/article/Colombia.png',
        'country': 'Colombia',
        'currency': 'Peso colombiano',
      },
      {
        'icon': 'assets/images/article/CostaRica.png',
        'country': 'Costa Rica',
        'currency': 'Colón costarricense',
      },
      {
        'icon': 'assets/images/article/Ecuador.png',
        'country': 'Ecuador',
        'currency': 'Dólar estadounidense',
      },
      {
        'icon': 'assets/images/article/Panama.png',
        'country': 'Panamá',
        'currency': 'Balboa',
      },
    ];

    return payMethods.map((method) {
      return Column(
        children: [
          Image.asset(
            method['icon']!,
            width: 80.sp,
          ),
          SizedBox(height: 12.sp),
          Text(
            method['country']!,
            style: TextStyle(
              fontSize: 14.sp,
              height: 1.0,
              color: Color(0xFF7F7F7F),
            ),
          ),
          SizedBox(height: 6.sp),
          Text(
            method['currency']!,
            style: TextStyle(
              fontSize: 16.sp,
              height: 1.0,
            ),
          ),
        ],
      );
    }).toList();
  }
}
