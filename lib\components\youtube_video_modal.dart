import 'package:flutter/material.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';
import 'package:chilat2_mall_app/components/optimized_youtube_player.dart';

/// 优化的YouTube视频模态框组件
class YouTubeVideoModal extends StatefulWidget {
  final String videoId;
  final String? title;

  const YouTubeVideoModal({
    Key? key,
    required this.videoId,
    this.title,
  }) : super(key: key);

  @override
  State<YouTubeVideoModal> createState() => _YouTubeVideoModalState();
}

class _YouTubeVideoModalState extends State<YouTubeVideoModal> {
  late YoutubePlayerController _controller;
  bool _isPlayerReady = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    _controller = YoutubePlayerController.fromVideoId(
      videoId: widget.videoId,
      autoPlay: false, // 不自动播放，减少性能消耗
      params: const YoutubePlayerParams(
        mute: false,
        showControls: true,
        showFullscreenButton: true,
        loop: false,
        enableCaption: false, // 禁用字幕减少资源消耗
        strictRelatedVideos: true, // 只显示相关视频
        // 添加渲染优化参数
        enableJavaScript: true,
      ),
    );

    // 延迟更新状态，确保播放器有时间初始化
    Future.delayed(Duration(milliseconds: 1500), () {
      if (mounted && !_isPlayerReady) {
        setState(() {
          _isPlayerReady = true;
          _isLoading = false;
        });
      }
    });
  }

  @override
  void dispose() {
    // 确保正确释放资源
    _controller.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.all(16),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        height: MediaQuery.of(context).size.width * 0.95 * 9 / 16 +
            60, // 16:9比例 + 控制栏高度
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            // 视频播放器容器
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                child: _isLoading
                    ? _buildLoadingWidget()
                    : OptimizedYouTubePlayer(
                        controller: _controller,
                        aspectRatio: 16 / 9,
                      ),
              ),
            ),

            // 关闭按钮
            Positioned(
              top: 8,
              right: 8,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),

            // 标题（可选）
            if (widget.title != null)
              Positioned(
                bottom: 8,
                left: 8,
                right: 48,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    widget.title!,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 构建加载指示器
  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: Colors.red,
              strokeWidth: 3,
            ),
            SizedBox(height: 16),
            Text(
              '正在加载视频...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 显示YouTube视频模态框的便捷方法
class YouTubeModalHelper {
  /// 显示YouTube视频模态框
  static Future<void> showVideoModal(
    BuildContext context, {
    required String videoId,
    String? title,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.8),
      builder: (BuildContext context) {
        return YouTubeVideoModal(
          videoId: videoId,
          title: title,
        );
      },
    );
  }

  /// 从YouTube URL提取视频ID
  static String? extractVideoId(String url) {
    final regExp = RegExp(
      r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
      caseSensitive: false,
    );
    final match = regExp.firstMatch(url);
    return match?.group(1);
  }

  /// 验证视频ID格式
  static bool isValidVideoId(String videoId) {
    return RegExp(r'^[a-zA-Z0-9_-]{11}$').hasMatch(videoId);
  }
}
