import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/utils/request.dart';

class ProductAPI {
  // 商品详情
  static Future<dynamic> useGoodsDetail(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/GoodsDetailPage/getPageData', params: data);

    return response;
  }

  // 商品列表
  static Future<dynamic> useGoodsListPage(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/GoodsListPage/getPageData', params: data);

    return response;
  }

  // 商品列表页-商品详情数据
  static Future<dynamic> useGoodsInfo(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/GoodsDetailPage/getGoodsInfo', params: data);

    return response;
  }

  // 获取首页最新商品分页数据 & 获取商品列表分页数据
  static Future<dynamic> useGoodsPageListData(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/GoodsListPage/searchGoods', params: data);

    return response;
  }

  // 查询邮箱是否已验证
  static Future<dynamic> useQueryVerifyMailResult(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/User/queryVerifyMailResult', params: data);

    return response;
  }

  // 以图搜图图片上传
  static Future<dynamic> useUploadImage1688(dynamic data) async {
    var response = await RequestUtil()
        .post('commodity/GoodsSearch/uploadImage1688', params: data);
    return response;
  }

  // 以图搜图-根据1688id获取商品信息
  static Future<dynamic> useGetGoods(dynamic data) async {
    var response =
        await RequestUtil().post('pages/GoodsListPage/getGoods', params: data);
    return response;
  }

  // 查询商品分类树
  static Future<dynamic> useCategoryTree(dynamic data) async {
    var response = await RequestUtil().post(
        'marketing/MarketingCategory/getMarketingCategoryTree',
        params: data);

    return response;
  }

  // 根据“促销活动代码”查询促销活动商品索引页信息
  static Future<dynamic> useGetPromotionGoodsIndexByCode(dynamic data) async {
    var response = await RequestUtil().post(
        '/marketing/PromotionActivity/getPromotionGoodsIndexByCode',
        params: data);

    return response;
  }
}
