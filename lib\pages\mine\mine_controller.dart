import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/services/user.dart';
import 'package:chilat2_mall_app/constants/config.dart';

class MineController extends GetxController {
  static MineController get to => Get.find();

  final Rx<Map<String, dynamic>?> userInfo = Rx<Map<String, dynamic>?>(null);
  final loading = true.obs;

  bool get isLoggedIn => Global.isLogin.value;

  @override
  void onInit() {
    super.onInit();
    loadUserInfo();

    ever(Global.isLogin, (isLoggedIn) {
      loadUserInfo();
    });
  }

  Future<void> loadUserInfo() async {
    try {
      loading.value = true;
      var storedUser = LocalStorage().getJSON(USER_INFO);

      if (Global.isLogin.value && storedUser != null) {
        userInfo.value = Map<String, dynamic>.from(storedUser);

        try {
          var res = await UserAPI.useUserDetail({});
          if (res != null &&
              res['result']['code'] == 200 &&
              res['data'] != null) {
            final newUserInfo = Map<String, dynamic>.from(res['data']);
            userInfo.value = newUserInfo;
          }
        } catch (e) {
          print("Error refreshing user info: $e");
        }
      } else {
        userInfo.value = null;
        await LocalStorage().remove(USER_INFO);
      }
    } catch (e) {
      userInfo.value = null;
      showErrorMessage(e.toString());
    } finally {
      loading.value = false;
    }
  }

  Future<void> logout() async {
    try {
      await UserAPI.useLogout({});
      userInfo.value = null;
      await LocalStorage().remove(USER_INFO);
      // 更新全局登录状态
      await Global.updateLoginStatus();
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }
}
