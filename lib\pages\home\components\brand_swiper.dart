import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:flutter/material.dart';
import 'package:chilat2_mall_app/components/left_title.dart';
import 'package:chilat2_mall_app/utils/my_navigator.dart';
import '../home_model.dart';

class BrandSwiper extends StatelessWidget {
  final List<BrandListElement> brandList;
  const BrandSwiper({Key? key, required this.brandList}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(15),
      margin: const EdgeInsets.only(top: 7.5, right: 15, bottom: 7.5, left: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Column(
        children: <Widget>[
          const LeftTitle(
            tipColor: AppColors.primaryColor,
            title: '品牌专场',
          ),
          Container(
            height: 90,
            padding: const EdgeInsets.only(top: 15),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: brandList.length,
              itemBuilder: (context, index) {
                return _buildBrandItem(context, index);
              },
            ),
          )
        ],
      ),
    );
  }

///////////////////////////////
  /// 每个品牌Item
  Widget _buildBrandItem(BuildContext context, int index) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // TODO: 实现品牌页面导航
        // MyNavigator.push(BrandDetailPage(brandId: brandList[index].id));
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('品牌详情页面开发中')),
        );
      },
      child: Container(
        padding: const EdgeInsets.only(right: 25),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Container(
              height: 50.0,
              width: 50.0,
              padding: const EdgeInsets.only(bottom: 12),
              child: MyCachedNetworkImage(
                imageUrl: brandList[index].icon,
                width: 50.0,
                height: 50.0,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            Text(
              brandList[index].name,
              style: const TextStyle(
                color: AppColors.primaryText,
                fontSize: 12,
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
