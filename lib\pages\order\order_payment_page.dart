import 'package:chilat2_mall_app/components/common_webview_page.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/services/order.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:async';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/styles/styles.dart';

class OrderPaymentPage extends StatefulWidget {
  const OrderPaymentPage({Key? key}) : super(key: key);

  @override
  State<OrderPaymentPage> createState() => _OrderPaymentPageState();
}

class _OrderPaymentPageState extends State<OrderPaymentPage> {
  final String orderNo = Get.parameters['orderNo'] ?? '';
  final String paymentId = Get.parameters['paymentId'] ?? '';

  bool isLoading = false;
  bool isPaying = false;
  bool showPaymentSuccess = false;
  bool showPayInfoError = false;
  bool couponInfoError = false;
  int timeLeft = 3;
  Timer? _countdownTimer;

  Map<String, dynamic> pageData = {
    'totalCount': 0,
    'picUrl': '',
    'goodsName': '',
    'payMethodList': [],
    'payAmount': {},
  };

  String selectedPaymentMethod = '';
  String paymentUrl = '';

  bool showDrawerWidget = false;
  bool showAmountDetails = false;
  final Duration _drawerAnimDuration = Duration(milliseconds: 260);

  double get _bottomBarHeight => 60.sp; // 和底部栏高度保持一致

  void _openAmountDetailsDrawer() {
    setState(() {
      showDrawerWidget = true;
      showAmountDetails = true;
    });
  }

  void _closeAmountDetailsDrawer() {
    setState(() {
      showAmountDetails = false;
    });
  }

  @override
  void initState() {
    super.initState();
    _getCashDeskInfo();
  }

  Future<void> _getCashDeskInfo() async {
    setState(() => isLoading = true);
    try {
      final response = await OrderService.useGetCashDeskInfo({
        'orderNo': orderNo,
        'paymentId': paymentId,
      });

      if (response['result']['code'] == 200) {
        setState(() {
          pageData = response['data'];
          // 自动选择支付方式
          for (var pay in pageData['payMethodList']) {
            final payMax = pay['max'];
            final payAmount = pageData['payAmount']?['amount'];
            if (payMax != null && payAmount != null && payMax >= payAmount) {
              selectedPaymentMethod = pay['id'];
              break;
            }
          }
        });
        // 校验支付状态
        if (pageData['payResultModel']?['payStatus'] == 'PAID_SUCCESS') {
          Get.offNamed(AppRoutes.OrderPayResults, parameters: {
            'orderNo': orderNo,
            'paymentId': paymentId,
          });
        }
      } else {
        setState(() {
          showPayInfoError = true;
          couponInfoError = response['result']['code'] == 70116;
          timeLeft = 3;
        });
        _countdownTimer?.cancel();
        _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
          if (timeLeft == 1) {
            Get.offNamed(AppRoutes.OrderDetail,
                arguments: {'orderNo': orderNo});
            timer.cancel();
            setState(() {
              showPayInfoError = false;
            });
          } else {
            setState(() {
              timeLeft--;
            });
          }
        });
      }
    } catch (e) {
      Get.snackbar('错误提示', e.toString(), snackPosition: SnackPosition.TOP);
    } finally {
      setState(() => isLoading = false);
    }
  }

  Future<void> _submitPayment() async {
    if (selectedPaymentMethod.isEmpty) return;

    setState(() => isPaying = true);
    try {
      final payMethod = pageData['payMethodList'].firstWhere(
        (method) => method['id'] == selectedPaymentMethod,
      );
      final response = await OrderService.useSubmitPayment({
        'orderNo': orderNo,
        'paymentId': paymentId,
        'payMethodId': selectedPaymentMethod,
        'payMethod': payMethod['code'],
        'amount': pageData['payAmount']['amount'],
      });

      if (response['result']['code'] == 200) {
        final url = response['data']['payUrl'];
        if (url.isNotEmpty) {
          // 使用内嵌WebView打开支付链接
          await Get.to(() => CommonWebViewPage(
              url: url,
              title:
                  I18n.of(context)?.translate('cm_order.orderPay') ?? 'Pago'));
          // 延迟 300ms 再弹窗，避免跳转动画冲突
          Future.delayed(const Duration(milliseconds: 300), () {
            _showPaySuccessDialog();
          });
        } else {
          Get.snackbar('错误提示', '无法打开支付链接', snackPosition: SnackPosition.TOP);
        }
      } else {
        Get.snackbar('错误提示', response['result']['message'] ?? '',
            snackPosition: SnackPosition.TOP);
      }
    } catch (e) {
      Get.snackbar('错误提示', e.toString(), snackPosition: SnackPosition.TOP);
    } finally {
      setState(() => isPaying = false);
    }
  }

  Future<void> _onGetQueryPayResult() async {
    try {
      final response = await OrderService.useQueryPayResult({
        'orderNo': orderNo,
        'paymentId': paymentId,
      });
      if (response['result']['code'] == 200) {
        final payStatus = response['data']['payResult']['payStatus'];
        setState(() => showPaymentSuccess = false);
        if (payStatus == 'INIT') {
          Future.delayed(const Duration(milliseconds: 100), () {
            Get.snackbar(
              '提示',
              I18n.of(context)?.translate('cm_order.continuePayment') ??
                  '请继续支付',
              snackPosition: SnackPosition.TOP,
            );
          });
        } else {
          // 跳转到支付结果页（replace 效果）
          Get.offNamed(AppRoutes.OrderPayResults, parameters: {
            'orderNo': orderNo,
            'paymentId': paymentId,
          });
        }
      }
    } catch (e) {
      Get.snackbar('错误提示', e.toString(), snackPosition: SnackPosition.TOP);
    }
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  Widget _buildAmountDetailsDrawerContent() {
    final List feeList = pageData['payAmount']?['feeList'] ?? [];
    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12.sp)),
        ),
        child: SafeArea(
          top: false,
          bottom: true,
          child: Padding(
            padding: EdgeInsets.only(
              left: 16.sp,
              right: 16.sp,
              top: 16.sp,
              bottom: _bottomBarHeight + 10.sp,
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        I18n.of(context)
                                ?.translate('cm_order.feeAmountDetails') ??
                            'Detalles',
                        style: TextStyle(
                            fontWeight: FontWeight.w500, fontSize: 18.sp),
                      ),
                      IconButton(
                        icon: Icon(Icons.close),
                        color: Colors.grey.shade700,
                        iconSize: 22.sp,
                        onPressed: _closeAmountDetailsDrawer,
                      ),
                    ],
                  ),
                  SizedBox(height: 6.sp),
                  ...feeList.map<Widget>((fee) {
                    final double? amount = fee['feeAmount'] is num
                        ? fee['feeAmount'].toDouble()
                        : double.tryParse('${fee['feeAmount']}');
                    final bool isNegative = amount != null && amount < 0;
                    return Padding(
                      padding: EdgeInsets.symmetric(vertical: 3.sp),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              fee['feeName'] ?? '',
                              style: TextStyle(fontSize: 14.sp),
                              softWrap: true,
                            ),
                          ),
                          SizedBox(width: 8.sp),
                          Expanded(
                            child: Text(
                              '${isNegative ? '-' : ''}US\$ ${amount != null ? amount.abs().toStringAsFixed(2) : ''}',
                              style: TextStyle(
                                fontSize: 14.sp,
                              ),
                              textAlign: TextAlign.right,
                              softWrap: true,
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                  Divider(height: 22.sp, thickness: 1),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Total',
                          style: TextStyle(
                              fontWeight: FontWeight.w500, fontSize: 16.sp)),
                      Text(
                        'US\$ ${(pageData['payAmount']?['amount'] ?? 0).toStringAsFixed(2)}',
                        style: TextStyle(
                            fontWeight: FontWeight.w500, fontSize: 16.sp),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    if (pageData['payAmount']?['amount'] == null) {
      return const SizedBox.shrink();
    }
    return Container(
      height: _bottomBarHeight,
      padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 10.sp),
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () {
              if (showAmountDetails) {
                _closeAmountDetailsDrawer();
              } else {
                _openAmountDetailsDrawer();
              }
            },
            child: Row(
              children: [
                Text(
                  'Total',
                  style: TextStyle(fontSize: 16.sp),
                ),
                SizedBox(width: 6.sp),
                Text(
                  'US\$ ${(pageData['payAmount']?['amount'] ?? 0).toStringAsFixed(2)}',
                  style:
                      TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
                ),
                SizedBox(width: 4.sp),
                Icon(
                  showAmountDetails
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Colors.grey.shade700,
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: (isPaying || selectedPaymentMethod.isEmpty)
                ? null
                : _submitPayment,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.sp)),
              padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 6.sp),
              minimumSize: Size(120.sp, 36.sp),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isPaying)
                  Container(
                    width: 16.sp,
                    height: 16.sp,
                    margin: EdgeInsets.only(right: 6.sp),
                    child: CircularProgressIndicator(
                        strokeWidth: 2, color: Colors.white),
                  ),
                Text(
                  I18n.of(context)?.translate('cm_order.payNow') ??
                      'Pagar ahora',
                  style: TextStyle(fontSize: 15.sp, color: Colors.white),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderInfo() {
    if (pageData['payAmount']?['amount'] == null) {
      return const SizedBox.shrink();
    }
    return Container(
      margin: EdgeInsets.only(bottom: 12.sp),
      padding:
          EdgeInsets.only(top: 18.sp, bottom: 18.sp, left: 8.sp, right: 8.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: InkWell(
        onTap: () =>
            Get.toNamed('/order/detail', parameters: {'orderNo': orderNo}),
        child: Row(
          children: [
            if (pageData['picUrl']?.isNotEmpty == true)
              MyCachedNetworkImage(
                imageUrl: pageData['picUrl'],
                width: 50.sp,
                height: 50.sp,
                fit: BoxFit.cover,
              ),
            SizedBox(width: 10.sp),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${pageData['totalCount']} ${I18n.of(context)?.translate('cm_order.orderItems') ?? 'Items'}',
                    style: const TextStyle(
                      decoration: TextDecoration.underline,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 2.sp),
                  Text(
                    pageData['goodsName'] ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(fontSize: 12.sp),
                  ),
                  SizedBox(height: 2.sp),
                  Text(
                    '${I18n.of(context)?.translate('cm_order.orderNo') ?? 'Order No.'}$orderNo',
                    style: TextStyle(fontSize: 12.sp),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethods() {
    if (pageData['payMethodList']?.isEmpty ?? true) {
      return const SizedBox.shrink();
    }
    return Container(
      margin: EdgeInsets.only(bottom: 10.sp),
      padding:
          EdgeInsets.only(top: 18.sp, bottom: 2.sp, left: 8.sp, right: 8.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.sp),
      ),
      child: Column(
        children: [
          for (var method in pageData['payMethodList'])
            _buildPaymentMethodItem(method),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodItem(Map<String, dynamic> method) {
    final methodMax = method['max'];
    final payAmount = pageData['payAmount']?['amount'] ?? 0;
    final bool isDisabled = methodMax != null && methodMax < payAmount;
    final bool isSelected = selectedPaymentMethod == method['id'];

    return Container(
      margin: EdgeInsets.only(bottom: 16.sp),
      child: InkWell(
        onTap: isDisabled
            ? null
            : () {
                setState(() => selectedPaymentMethod = method['id']);
              },
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 8.sp, vertical: 10.sp),
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFFF9E5E6) : Colors.white,
            border: Border.all(
              color: isSelected ? AppColors.primaryColor : Colors.grey.shade300,
            ),
            borderRadius: BorderRadius.circular(4.sp),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 18.sp,
                height: 18.sp,
                child: Radio<String>(
                  value: method['id'],
                  groupValue: selectedPaymentMethod,
                  activeColor: AppColors.primaryColor,
                  onChanged: isDisabled
                      ? null
                      : (value) {
                          setState(() => selectedPaymentMethod = value!);
                        },
                  visualDensity: VisualDensity.standard,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  fillColor: MaterialStateProperty.resolveWith<Color>((states) {
                    if (isDisabled) {
                      return const Color(0xFFC2C2C2); // 禁用时圆圈变灰
                    }
                    if (states.contains(MaterialState.selected)) {
                      return AppColors.primaryColor; // 选中为红色
                    }
                    return Colors.grey.shade400; // 默认灰色
                  }),
                ),
              ),
              SizedBox(width: 6.sp),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      method['name'],
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: isDisabled
                            ? const Color.fromRGBO(194, 194, 194, 1)
                            : null,
                      ),
                    ),
                    if (isDisabled)
                      Text(
                        '${I18n.of(context)?.translate('cm_order.orderPayLimit') ?? 'Payment Limit'} ${method['max']}',
                        style: TextStyle(
                          color: Color(0xFFE15A47),
                          fontSize: 12.sp,
                        ),
                      ),
                  ],
                ),
              ),
              Container(
                height: 40.sp,
                constraints: BoxConstraints(
                  maxWidth: 170.sp,
                ),
                padding:
                    EdgeInsets.symmetric(vertical: 11.sp, horizontal: 4.sp),
                decoration: BoxDecoration(
                  color: const Color(0xFFF2F2F2),
                ),
                child: _buildPaymentIcon(method['iconUrl']),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建支付方式图标，支持SVG和普通图片
  Widget _buildPaymentIcon(String? iconUrl) {
    if (iconUrl == null || iconUrl.isEmpty) {
      return const Icon(Icons.payment, color: Colors.grey);
    }

    // 判断是否为SVG格式
    final bool isSvg = iconUrl.toLowerCase().endsWith('.svg');

    if (isSvg) {
      return SvgPicture.network(
        iconUrl,
        fit: BoxFit.contain,
        placeholderBuilder: (context) => const Center(
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
        // SVG加载失败时显示默认图标
        errorBuilder: (context, error, stackTrace) => const Icon(
          Icons.payment,
          color: Colors.grey,
        ),
      );
    } else {
      return MyCachedNetworkImage(
        imageUrl: iconUrl,
        fit: BoxFit.contain,
      );
    }
  }

  Widget _buildPayInfoErrorDialog() {
    return Center(
      child: Container(
        width: 340.sp,
        padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 22.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.sp),
          boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 16)],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(Icons.error_outline,
                    color: AppColors.primaryColor, size: 20.sp),
                SizedBox(width: 6.sp),
                Expanded(
                  child: Text(
                    couponInfoError
                        ? (I18n.of(context)
                                ?.translate('cm_order.chooseCouponError') ??
                            '优惠券信息报错')
                        : (I18n.of(context)
                                ?.translate('cm_order.orderChanged') ??
                            '订单已变更'),
                    style: TextStyle(
                      fontSize: 14.sp,
                    ),
                    textAlign: TextAlign.left,
                    softWrap: true,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.sp),
            Divider(height: 1.sp, color: const Color(0xFFEEEEEE)),
            SizedBox(height: 12.sp),
            Padding(
              padding: EdgeInsets.only(top: 0.sp),
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '$timeLeft',
                      style: TextStyle(
                        fontSize: 20.sp,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    TextSpan(
                      text: ' ',
                    ),
                    TextSpan(
                      text: I18n.of(context)
                              ?.translate('cm_order.orderRedirectDetails') ??
                          'segundos después, serás redirigido automáticamente a la página de detalles del pedido.',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ],
                ),
                textAlign: TextAlign.left,
                softWrap: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaySuccessDialog() {
    return Positioned.fill(
      child: Material(
        color: Colors.black.withOpacity(0.25),
        child: Center(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 24.sp, vertical: 24.sp),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.sp),
            ),
            // width: 320.sp,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                    I18n.of(context)?.translate('cm_order.isPaySuccess') ??
                        '是否支付成功',
                    style: TextStyle(
                        fontSize: 18.sp, fontWeight: FontWeight.bold)),
                SizedBox(height: 16.sp),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    OutlinedButton(
                      onPressed: _onGetQueryPayResult,
                      style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.sp, vertical: 8.sp)),
                      child: Text(
                          I18n.of(context)
                                  ?.translate('cm_order.orderCancelNo') ??
                              '未支付',
                          style: TextStyle(fontSize: 14.sp)),
                    ),
                    SizedBox(width: 12.sp),
                    ElevatedButton(
                      onPressed: _onGetQueryPayResult,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.sp, vertical: 8.sp),
                      ),
                      child: Text(
                          I18n.of(context)
                                  ?.translate('cm_order.orderCancelYes') ??
                              '已支付',
                          style:
                              TextStyle(fontSize: 14.sp, color: Colors.white)),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showPaySuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.sp)),
          contentPadding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                I18n.of(context)?.translate('cm_order.isPaySuccess') ??
                    '¿El pago se realizó correctamente?',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: const Color(0xFFE0E0E0)),
                      foregroundColor: const Color(0xFF6B6B6B),
                      padding: EdgeInsets.symmetric(
                          horizontal: 12.sp, vertical: 0.sp),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.sp)),
                      textStyle: TextStyle(fontSize: 16.sp),
                    ),
                    onPressed: () {
                      Navigator.of(context).pop(); // 关闭对话框
                      Get.offNamed(AppRoutes.OrderPayResults, parameters: {
                        'orderNo': orderNo,
                        'paymentId': paymentId,
                      });
                    },
                    child: Text(
                        I18n.of(context)?.translate('cm_order.orderCancelNo') ??
                            'No'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                          horizontal: 12.sp, vertical: 0.sp),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.sp)),
                      textStyle: TextStyle(fontSize: 16.sp),
                      elevation: 0,
                    ),
                    onPressed: () {
                      Navigator.of(context).pop(); // 关闭对话框
                      Get.offNamed(AppRoutes.OrderPayResults, parameters: {
                        'orderNo': orderNo,
                        'paymentId': paymentId,
                      });
                    },
                    child: Text(I18n.of(context)
                            ?.translate('cm_order.orderCancelYes') ??
                        'Sí.'),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // 尝试返回，如果导航栈为空，则跳转到订单详情页
        if (Navigator.of(context).canPop()) {
          Get.back();
        } else {
          // 如果无法返回，则导航到订单详情页
          Get.offNamed(AppRoutes.OrderDetail, arguments: {'orderNo': orderNo});
        }
        return false;
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        body: Stack(
          children: [
            AppBar(
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  // 尝试返回，如果导航栈为空，则跳转到订单详情页
                  if (Navigator.of(context).canPop()) {
                    Get.back();
                  } else {
                    // 如果无法返回，则导航到订单详情页
                    Get.offNamed(AppRoutes.OrderDetail,
                        arguments: {'orderNo': orderNo});
                  }
                },
              ),
              title: Text(
                I18n.of(context)?.translate('cm_order.orderPay') ?? 'Pago',
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                top: kToolbarHeight + MediaQuery.of(context).padding.top,
                bottom: _bottomBarHeight, // 为底部栏预留空间
              ),
              child: isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : Container(
                      color: const Color(0xFFF5F5F5),
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 8.sp, vertical: 10.sp),
                          child: Column(
                            children: [
                              _buildOrderInfo(),
                              _buildPaymentMethods(),
                            ],
                          ),
                        ),
                      ),
                    ),
            ),
            // 明细抽屉弹出时，底部栏在最上层且不被遮盖
            if (showDrawerWidget) ...[
              Positioned.fill(
                child: GestureDetector(
                  onTap: _closeAmountDetailsDrawer,
                  child: Container(
                    color: Colors.black.withOpacity(0.4),
                  ),
                ),
              ),
              _AnimatedBottomDrawer(
                visible: showAmountDetails,
                duration: _drawerAnimDuration,
                child: _buildAmountDetailsDrawerContent(),
                onDismissed: () => setState(() => showDrawerWidget = false),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: _buildBottomBar(),
              ),
            ],
            // 其他弹窗弹出时，底部栏和主内容一起被遮盖
            if (!showDrawerWidget &&
                (showPayInfoError || showPaymentSuccess)) ...[
              Positioned.fill(
                child: Container(color: Colors.black.withOpacity(0.4)),
              ),
              if (showPayInfoError) _buildPayInfoErrorDialog(),
              if (showPaymentSuccess) _buildPaySuccessDialog(),
              // 这里也渲染底部栏，保证即使被遮盖也有布局
              Align(
                alignment: Alignment.bottomCenter,
                child: _buildBottomBar(),
              ),
            ],
            // 没有弹窗时，底部栏正常显示
            if (!showDrawerWidget && !(showPayInfoError || showPaymentSuccess))
              Align(
                alignment: Alignment.bottomCenter,
                child: _buildBottomBar(),
              ),
          ],
        ),
      ),
    );
  }
}

class _AnimatedBottomDrawer extends StatefulWidget {
  final bool visible;
  final Duration duration;
  final Widget child;
  final VoidCallback? onDismissed;

  const _AnimatedBottomDrawer({
    required this.visible,
    required this.duration,
    required this.child,
    this.onDismissed,
  });

  @override
  State<_AnimatedBottomDrawer> createState() => _AnimatedBottomDrawerState();
}

class _AnimatedBottomDrawerState extends State<_AnimatedBottomDrawer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnim;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    _offsetAnim = Tween<Offset>(
      begin: Offset(0, 1),
      end: Offset(0, 0),
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));
    if (widget.visible) _controller.forward();
    _controller.addStatusListener(_handleAnimStatus);
  }

  void _handleAnimStatus(AnimationStatus status) {
    if (status == AnimationStatus.dismissed && !widget.visible) {
      widget.onDismissed?.call();
    }
  }

  @override
  void didUpdateWidget(_AnimatedBottomDrawer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.visible && !_controller.isCompleted) {
      _controller.forward();
    } else if (!widget.visible && _controller.isCompleted) {
      _controller.reverse();
    }
  }

  @override
  void dispose() {
    _controller.removeStatusListener(_handleAnimStatus);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: SlideTransition(
        position: _offsetAnim,
        child: widget.child,
      ),
    );
  }
}
