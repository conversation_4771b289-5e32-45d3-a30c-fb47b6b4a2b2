import 'package:flutter/material.dart';

class Result {
  dynamic code;
  dynamic message;

  Result({
    this.code,
    this.message,
  });

  // 工厂方法，用于从JSON数据创建User对象实例
  factory Result.fromJson(dynamic json) {
    return Result(
      code: json?['code'] as int,
      message: json?['message'],
    );
  }
}

class NameValueModel {
  String? name;
  String? value;

  NameValueModel({
    this.name,
    this.value,
  });

  factory NameValueModel.fromJson(Map<String, dynamic> json) {
    return NameValueModel(
      name: json['name'] as String,
      value: json['value'] as String,
    );
  }
}

enum CurrencyType {
  CURRENCY_TYPE_UNSPECIFIED,
  CURRENCY_TYPE_CNY, //人民币
  CURRENCY_TYPE_USD, //美元
  CURRENCY_TYPE_ARS, //阿根廷比索
  CURRENCY_TYPE_BOB, //玻利维亚诺
  CURRENCY_TYPE_CLP, //智利比索
  CURRENCY_TYPE_COP, //哥伦比亚比索
  CURRENCY_TYPE_CRC, //哥斯达黎加科朗
  CURRENCY_TYPE_MXN, //墨西哥比索
  CURRENCY_TYPE_PEN, //秘鲁索尔
}

CurrencyType? strToCurrencyType(String? value) {
  if (value == null) return null;
  switch (value.toLowerCase()) {
    case 'currency_type_cny':
      return CurrencyType.CURRENCY_TYPE_CNY;
    case 'currency_type_usd':
      return CurrencyType.CURRENCY_TYPE_USD;
    case 'currency_type_ars':
      return CurrencyType.CURRENCY_TYPE_ARS;
    case 'currency_type_bob':
      return CurrencyType.CURRENCY_TYPE_BOB;
    case 'currency_type_clp':
      return CurrencyType.CURRENCY_TYPE_CLP;
    case 'currency_type_cop':
      return CurrencyType.CURRENCY_TYPE_COP;
    case 'currency_type_crc':
      return CurrencyType.CURRENCY_TYPE_CRC;
    case 'currency_type_mxn':
      return CurrencyType.CURRENCY_TYPE_MXN;
    case 'currency_type_pen':
      return CurrencyType.CURRENCY_TYPE_PEN;
    default:
      return CurrencyType.CURRENCY_TYPE_UNSPECIFIED;
  }
}

extension CurrencyTypeExtension on CurrencyType {
  String toShortString() {
    return toString().split('.').last;
  }

  static CurrencyType? fromString(String? value) {
    if (value == null) return null;

    switch (value.toLowerCase()) {
      case 'currency_type_cny':
        return CurrencyType.CURRENCY_TYPE_CNY;
      case 'currency_type_usd':
        return CurrencyType.CURRENCY_TYPE_USD;
      case 'currency_type_ars':
        return CurrencyType.CURRENCY_TYPE_ARS;
      case 'currency_type_bob':
        return CurrencyType.CURRENCY_TYPE_BOB;
      case 'currency_type_clp':
        return CurrencyType.CURRENCY_TYPE_CLP;
      case 'currency_type_cop':
        return CurrencyType.CURRENCY_TYPE_COP;
      case 'currency_type_crc':
        return CurrencyType.CURRENCY_TYPE_CRC;
      case 'currency_type_mxn':
        return CurrencyType.CURRENCY_TYPE_MXN;
      case 'currency_type_pen':
        return CurrencyType.CURRENCY_TYPE_PEN;
      default:
        return CurrencyType.CURRENCY_TYPE_UNSPECIFIED;
    }
  }
}

class SeoData {
  String title;
  String keyword;
  String description;
  String? headFirstScript;
  String? headLastScript;
  String? bodyFirstContent;
  String? bodyLastContent;
  List<NameValueModel> responseHeaders;

  SeoData({
    required this.title,
    required this.keyword,
    required this.description,
    this.headFirstScript,
    this.headLastScript,
    this.bodyFirstContent,
    this.bodyLastContent,
    required this.responseHeaders,
  });

  factory SeoData.fromJson(Map<String, dynamic> json) {
    List<NameValueModel> responseHeaders = [];

    if (json['responseHeaders'] != null) {
      json['responseHeaders'].forEach((v) {
        responseHeaders.add(NameValueModel.fromJson(v));
      });
    }

    return SeoData(
      title: json['title'] as String,
      keyword: json['keyword'],
      description: json['description'],
      headFirstScript: json['headFirstScript'] as String?,
      headLastScript: json['headLastScript'] as String?,
      bodyFirstContent: json['bodyFirstContent'] as String?,
      bodyLastContent: json['bodyLastContent'] as String?,
      responseHeaders: responseHeaders,
    );
  }
}

// 类目
class MallCategoryTreeModel {
  String id;
  dynamic cateName;
  dynamic cateLogo;
  dynamic goodsCount;
  List<MallCategoryTreeModel> children;

  MallCategoryTreeModel({
    required this.id,
    this.cateName,
    this.cateLogo,
    this.goodsCount,
    required this.children,
  });

  factory MallCategoryTreeModel.fromJson(Map<String, dynamic> json) {
    List<MallCategoryTreeModel> children = [];

    if (json['children'] != null) {
      json['children'].forEach((v) {
        children.add(MallCategoryTreeModel.fromJson(v));
      });
    }

    return MallCategoryTreeModel(
      id: json['id'] as String,
      cateName: json['cateName'],
      cateLogo: json['cateLogo'],
      goodsCount: json['goodsCount'],
      children: children,
    );
  }
}

// 商城前台商品分类路径明细
class MallCategoryPathItemModel {
  String id;
  String name;

  MallCategoryPathItemModel({
    required this.name,
    required this.id,
  });

  factory MallCategoryPathItemModel.fromJson(Map<String, dynamic> json) {
    return MallCategoryPathItemModel(
      name: json['name'] as String,
      id: json['id'] as String,
    );
  }
}

class PageParam {
  int current;
  int size;
  List<SortParam>? sort;

  PageParam({
    required this.current,
    required this.size,
    this.sort,
  });

  factory PageParam.fromJson(Map<String, dynamic> json) {
    return PageParam(
      current: json['current'] as int,
      size: json['size'] as int,
      sort: json['sort'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'size': size,
      'current': current,
      'sort': sort?.map((item) => item.toJson()).toList(),
    };
  }
}

class SortParam {
  int orderBy;
  int isAsc;

  SortParam({
    required this.orderBy,
    required this.isAsc,
  });

  factory SortParam.fromJson(Map<String, dynamic> json) {
    return SortParam(
      orderBy: json['orderBy'] as int,
      isAsc: json['isAsc'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orderBy': orderBy,
      'isAsc': isAsc,
    };
  }
}

class StepData {
  List<StepItem> steps;

  StepData({
    required this.steps,
  });

  factory StepData.fromJson(Map<String, dynamic> json) {
    List<StepItem> steps = [];
    if (json['steps'] != null) {
      json['steps'].forEach((v) {
        steps.add(StepItem.fromJson(v));
      });
    }

    return StepData(
      steps: steps,
    );
  }
}

class StepItem {
  String title;
  String description;
  IconData? icon;

  StepItem({
    required this.title,
    required this.description,
    this.icon,
  });

  factory StepItem.fromJson(dynamic json) {
    return StepItem(
      title: json?['title'],
      description: json?['description'],
      icon: json?['icon'] != null
          ? IconData(json?['icon'], fontFamily: 'MaterialIcons')
          : null,
    );
  }
}

class PageInfo {
  int current;
  int size;
  int pages;
  int total;

  PageInfo({
    this.current = 0,
    this.size = 20,
    this.pages = 0,
    this.total = 0,
  });

  factory PageInfo.fromJson(Map<String, dynamic> json) {
    return PageInfo(
      current: json['current'] ?? 0,
      size: json['size'] ?? 20,
      pages: json['pages'] ?? 0,
      total: json['total'] ?? 0,
    );
  }
}

enum GoodsAttributeItemType {
  GOODS_ATTRIBUTE_ITEM_TYPE_UNKNOWN,
  GOODS_ATTRIBUTE_ITEM_TYPE_INPUT, // 输入框
  GOODS_ATTRIBUTE_ITEM_TYPE_MULTI_OPTION, // 多选项
  GOODS_ATTRIBUTE_ITEM_TYPE_SELECT // 下拉框
}

GoodsAttributeItemType? stringToGoodsAttributeItemType(String value) {
  switch (value) {
    case 'GOODS_ATTRIBUTE_ITEM_TYPE_INPUT':
      return GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_INPUT;
    case 'GOODS_ATTRIBUTE_ITEM_TYPE_MULTI_OPTION':
      return GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_MULTI_OPTION;
    case 'GOODS_ATTRIBUTE_ITEM_TYPE_SELECT':
      return GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_SELECT;
    default:
      return GoodsAttributeItemType.GOODS_ATTRIBUTE_ITEM_TYPE_UNKNOWN;
  }
}

enum GoodsListFilterType {
  GOODS_LIST_FILTER_TYPE_UNSPECIFIED,
  GOODS_LIST_FILTER_TYPE_CATEGORY, //商品分类过滤
  GOODS_LIST_FILTER_TYPE_PRICE, //价格范围过滤
  GOODS_LIST_FILTER_TYPE_MIN_BUY, //最少购买过滤
}

GoodsListFilterType? goodsListFilterTypeFromJSON(String value) {
  switch (value) {
    case 'GOODS_LIST_FILTER_TYPE_CATEGORY':
      return GoodsListFilterType.GOODS_LIST_FILTER_TYPE_CATEGORY;
    case 'GOODS_LIST_FILTER_TYPE_PRICE':
      return GoodsListFilterType.GOODS_LIST_FILTER_TYPE_PRICE;
    case 'GOODS_LIST_FILTER_TYPE_MIN_BUY':
      return GoodsListFilterType.GOODS_LIST_FILTER_TYPE_MIN_BUY;
    default:
      return GoodsListFilterType.GOODS_LIST_FILTER_TYPE_UNSPECIFIED;
  }
}

enum JobDuty {
  JOB_DUTY_UNKNOWN,
  JOB_DUTY_SALESMAN,
  JOB_DUTY_PURCHASER,
}

JobDuty? jobDutyFromJSON(String value) {
  switch (value) {
    case 'JOB_DUTY_SALESMAN':
      return JobDuty.JOB_DUTY_SALESMAN;
    case 'JOB_DUTY_PURCHASER':
      return JobDuty.JOB_DUTY_PURCHASER;
    default:
      return JobDuty.JOB_DUTY_UNKNOWN;
  }
}

enum GoodsSelectorType {
  GOODS_SELECTOR_TYPE_UNSPECIFIED,
  GOODS_SELECTOR_TYPE_MARKETING_RULE,
  GOODS_SELECTOR_TYPE_GOODS_TAG,
}

GoodsSelectorType? goodsSelectorTypeFromJSON(String value) {
  switch (value) {
    case 'GOODS_SELECTOR_TYPE_MARKETING_RULE':
      return GoodsSelectorType.GOODS_SELECTOR_TYPE_MARKETING_RULE;
    case 'GOODS_SELECTOR_TYPE_GOODS_TAG':
      return GoodsSelectorType.GOODS_SELECTOR_TYPE_GOODS_TAG;
    default:
      return GoodsSelectorType.GOODS_SELECTOR_TYPE_UNSPECIFIED;
  }
}
