import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/goods_price.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/pages/product/components/goods_spec.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 基于MidSearchGoodsModel协议设计的商品卡片
class MidSearchGoodsCard extends StatelessWidget {
  final dynamic goods;
  final double imageHeight; // 图片高度
  final double imageWidth; // 图片宽度

  const MidSearchGoodsCard(
      {super.key,
      required this.goods,
      this.imageHeight = 96,
      this.imageWidth = 136});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        NavigatorUtil.pushNamed(context, AppRoutes.ProductPage, arguments: {
          'productId': goods?['goodsId'],
          "padc": goods?['padc']
        });
      },
      child: Container(
        margin: EdgeInsets.only(top: 4.sp, left: 4.sp, bottom: 4.sp),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MyCachedNetworkImage(
              imageUrl: goods?['mainImageUrl'] ?? '',
              width: imageWidth,
              height: imageHeight,
              borderRadius: BorderRadius.circular(4),
            ),
            Container(
              padding: EdgeInsets.only(top: 4.sp, left: 4.sp),
              width: imageWidth,
              child: Text(
                goods?['goodsName'] ?? '',
                style: TextStyle(
                  height: 1.1,
                  color: Colors.black54,
                  fontWeight: FontWeight.w500,
                ),
                // 最多显示两行
                maxLines: 2,
                // 超出部分用省略号
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Container(
              padding: EdgeInsets.only(left: 4.sp, top: 2.sp),
              child: Text(
                setUnit(goods?['minPrice'] ?? 0),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade800,
                ),
              ),
            ),
            Visibility(
                visible: goods?['pcsEstimateFreight'] != null,
                child: Container(
                  padding: EdgeInsets.only(top: 0.sp),
                  child:
                      Text(I18n.of(context)!.translate('cm_goods.shippingCost'),
                          style: TextStyle(
                            color: Colors.black54,
                          )),
                )),
            Visibility(
                visible: goods?['pcsEstimateFreight'] != null,
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: setUnit(goods?['pcsEstimateFreight']),
                        style: TextStyle(
                          color: Colors.black54,
                        ),
                      ),
                      TextSpan(
                        text: '/',
                        style: TextStyle(
                          color: Colors.black54,
                        ),
                      ),
                      TextSpan(
                        text: '${goods?['goodsPriceUnitName'] ?? ''}',
                        style: TextStyle(
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
}

// 基于GoodsListDataItemModel协议设计的商品卡片
class GoodsListDataCard extends StatelessWidget {
  final double imageWidth; // 图片宽度
  final double imageHeight; // 图片高度
  final GoodsListDataItemModel goods;
  const GoodsListDataCard(
      {super.key,
      required this.goods,
      this.imageWidth = 136,
      this.imageHeight = 108});

  @override
  Widget build(BuildContext context) {
    return InkWell(
        onTap: () {
          NavigatorUtil.pushNamed(context, AppRoutes.ProductPage,
              arguments: {'productId': goods.goodsId, "padc": goods.padc});
        },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MyCachedNetworkImage(
                imageUrl: goods.mainImageUrl ?? '',
                fit: BoxFit.cover,
              ),
              Container(
                padding: EdgeInsets.only(top: 4.sp, left: 4.sp),
                child: Text(
                  goods.goodsName ?? '',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                    color: Colors.grey[600],
                    height: 1.2,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.only(left: 4.sp, top: 2.sp),
                child: Text(
                  setUnit(goods.minPrice ?? 0),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800,
                  ),
                ),
              ),
              Visibility(
                  visible: goods.pcsEstimateFreight != null,
                  child: Container(
                    padding: EdgeInsets.only(top: 0.sp, left: 4.sp),
                    child: Text(
                        I18n.of(context)!.translate('cm_goods.shippingCost'),
                        style: TextStyle(
                          color: Colors.black54,
                        )),
                  )),
              Visibility(
                  visible: goods.pcsEstimateFreight != null,
                  child: Container(
                    padding: EdgeInsets.only(left: 4.sp),
                    child: RichText(
                      text: TextSpan(
                        children: [
                          // TextSpan(
                          //   text: I18n.of(context)!
                          //       .translate('cm_goods.shippingCost'),
                          //   style: TextStyle(
                          //     color: Colors.black54,
                          //   ),
                          // ),
                          TextSpan(
                            text: setUnit(goods.pcsEstimateFreight ?? 0),
                            style: TextStyle(
                              color: Colors.black54,
                            ),
                          ),
                          TextSpan(
                            text: '/',
                            style: TextStyle(
                              color: Colors.black54,
                            ),
                          ),
                          TextSpan(
                            text: goods.goodsPriceUnitName ?? '',
                            style: TextStyle(
                              color: Colors.black54,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )),
            ],
          ),
        ));
  }
}

class GoodsListDataItem extends StatelessWidget {
  final double screenHeight;
  final GoodsListDataItemModel goods;
  final Function() onCartUpdate;
  const GoodsListDataItem(
      {super.key,
      this.screenHeight = 480,
      required this.goods,
      required this.onCartUpdate});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.sp, vertical: 2.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          NavigatorUtil.pushNamed(context, AppRoutes.ProductPage,
              arguments: {'productId': goods.goodsId, "padc": goods.padc});
        },
        child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
          MyCachedNetworkImage(
            imageUrl: goods.mainImageUrl ?? '',
            width: 102,
            height: 102,
            fit: BoxFit.cover, // 添加适配方式
            borderRadius: BorderRadius.circular(4),
          ),
          Expanded(
            flex: 3,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4.sp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    goods.goodsName ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Container(
                    padding: EdgeInsets.only(top: 4.sp),
                    child: Row(
                      children: [
                        Expanded(
                            flex: 5,
                            child: Column(children: [
                              Container(
                                alignment: Alignment.centerLeft,
                                child: Text.rich(TextSpan(children: [
                                  TextSpan(
                                    text: setUnit(goods.minPrice),
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w600),
                                  ),
                                  TextSpan(
                                    text: ' / ${goods.goodsPriceUnitName}',
                                    style: TextStyle(
                                        color: Colors.black54,
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.w600),
                                  ),
                                ])),
                              ),
                              Visibility(
                                visible: goods.pcsEstimateFreight != null,
                                child: Container(
                                  padding: EdgeInsets.only(left: 2),
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    '${I18n.of(context)?.translate('cm_goods.shippingCost')}${setUnit(goods?.pcsEstimateFreight)}/ ${goods?.goodsPriceUnitName}',
                                    style: TextStyle(
                                        fontSize: 13.sp,
                                        fontWeight: FontWeight.w400),
                                  ),
                                ),
                              ),
                            ])),
                        Expanded(
                            flex: 1,
                            child: GestureDetector(
                              behavior: HitTestBehavior.opaque, // 关键：拦截事件冒泡
                              onTap: () {
                                showModalBottomSheet(
                                    context: context,
                                    isScrollControlled: true,
                                    isDismissible: true, // 点击蒙层关闭
                                    enableDrag: false, // 拖动关闭
                                    builder: (BuildContext context) {
                                      return SizedBox(
                                        height: screenHeight * 0.8,
                                        child: GoodsSpecDrawer(
                                          context: context,
                                          goodsId: goods.goodsId ?? '',
                                          sourceGoodsId:
                                              goods.sourceGoodsId ?? '',
                                          selectEvent: (int index) {},
                                          onCartUpdate: (String goodsId) {
                                            onCartUpdate();
                                          },
                                          onGoodsSpecClose: () {
                                            Navigator.pop(context);
                                          },
                                        ),
                                      );
                                    });
                              },
                              child: Container(
                                padding: EdgeInsets.all(4.sp),
                                child: Icon(
                                  Icons.shopping_cart_outlined,
                                  color: goods.selected == true
                                      ? Colors.red
                                      : Colors.grey,
                                  size: 28.sp,
                                ),
                              ),
                            ))
                      ],
                    ),
                  )
                ],
              ),
            ),
          )
        ]),
      ),
    );
  }
}

// 基于GoodsListDataItemModel的JSON数据的商品卡片
class GoodsListDataItemCard extends StatelessWidget {
  final dynamic goods;
  final double goodsWidth;
  final double borderRadius;
  final double imageHeight;

  const GoodsListDataItemCard({
    super.key,
    required this.goods,
    this.goodsWidth = 150,
    this.borderRadius = 4,
    this.imageHeight = 128,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        NavigatorUtil.pushNamed(context, AppRoutes.ProductPage, arguments: {
          'productId': goods?['goodsId'],
          "padc": goods?['padc']
        });
      },
      child: Container(
        width: goodsWidth,
        padding: const EdgeInsets.only(bottom: 4),
        margin: const EdgeInsets.symmetric(horizontal: 2),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius),
          color: Colors.white,
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 商品图片
            Expanded(
              child: ClipRRect(
                borderRadius:
                    BorderRadius.vertical(top: Radius.circular(borderRadius)),
                child: MyCachedNetworkImage(
                  imageUrl: goods?['mainImageUrl'] ?? '',
                  fit: BoxFit.fill,
                  width: MediaQuery.sizeOf(context).width,
                  height: imageHeight,
                ),
              ),
            ),
            // 商品名称
            Padding(
              padding: const EdgeInsets.only(left: 4.0, top: 2.0),
              child: Text(
                goods?['goodsName'] ?? '',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontWeight: FontWeight.normal,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            // 商品价格
            Padding(
              padding: EdgeInsets.only(left: 4.0, top: 2.0, bottom: 2),
              child: GoodsPriceUnit(
                  minPrice: goods?['minPrice'] ?? 0,
                  maxPrice: goods?['maxPrice'] ?? 0,
                  textStyle: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800,
                  )),
            ),
          ],
        ),
      ),
    );
  }
}
