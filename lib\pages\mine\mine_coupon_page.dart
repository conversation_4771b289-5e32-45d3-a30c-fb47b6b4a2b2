import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:chilat2_mall_app/services/user.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/components/coupon_card.dart';
import 'package:chilat2_mall_app/pages/mine/components/email_validation.dart';
import 'package:chilat2_mall_app/components/app_scaffold.dart';
import 'package:chilat2_mall_app/utils/local_storage.dart';
import 'package:chilat2_mall_app/constants/config.dart';

class MineCouponPage extends StatefulWidget {
  const MineCouponPage({Key? key}) : super(key: key);

  @override
  State<MineCouponPage> createState() => _MineCouponPageState();
}

class _MineCouponPageState extends State<MineCouponPage>
    with SingleTickerProviderStateMixin {
  final RxList couponList = [].obs;
  final RxList pendingCouponList = [].obs;
  final RxBool isLoading = false.obs;
  final RxBool isNeedMailVerify = false.obs;
  final RxBool isMailVerified = false.obs;
  final RxString couponType = 'COUPON_TYPE_ALL'.obs;
  final RxString ticketStatus = 'TICKET_NOT_USE'.obs;
  final RxMap<String, dynamic> pageInfo = {
    'current': 1,
    'size': 10,
    'total': 0,
  }.obs;
  final RxBool isPageReady = false.obs;
  final RxBool hasMore = true.obs;

  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  final List<Map<String, String>> couponTabData = [
    {
      'value': 'TICKET_NOT_USE',
      'label': 'cm_coupon.unusedCoupon',
    },
    {
      'value': 'TICKET_USE',
      'label': 'cm_coupon.usedCoupon',
    },
    {
      'value': 'TICKET_LOSE_EFFICACY',
      'label': 'cm_coupon.expiredCoupon',
    },
  ];

  final List<Map<String, String>> couponTypeData = [
    {
      'value': 'COUPON_TYPE_ALL',
      'label': 'cm_coupon.allCoupon',
    },
    {
      'value': 'COUPON_TYPE_PRODUCT',
      'label': 'cm_coupon.productCoupon',
    },
    {
      'value': 'COUPON_TYPE_COMMISSION',
      'label': 'cm_coupon.commissionCoupon',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: couponTabData.length, vsync: this);
    _scrollController.addListener(_onScrollBottom);

    // 获取路由参数中的状态
    final arguments = Get.arguments;
    if (arguments != null) {
      ticketStatus.value = arguments['ticketStatus'] ?? 'TICKET_NOT_USE';
      couponType.value = arguments['couponType'] ?? '';

      // 设置选中的tab
      _tabController.index =
          couponTabData.indexWhere((tab) => tab['value'] == ticketStatus.value);
    }

    _initPage();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // 查询邮箱是否已验证
  Future<void> onQueryVerifyMailResult() async {
    final userInfo = LocalStorage().getJSON(USER_INFO);
    final res = await UserAPI.useQueryVerifyMailResult({
      'email': userInfo?['username'] ?? '',
      'isNeedCoupon': true,
      'verifyMailScene': 'MY_COUPON_LIST',
    });

    if (res['result']['code'] == 200) {
      isMailVerified.value = res['data']['isMailVerified'];
      if (isMailVerified.value) {
        return onGetCouponList();
      }
      pendingCouponList.value = res['data']['couponList'] ?? [];
      isNeedMailVerify.value =
          !isMailVerified.value && pendingCouponList.isNotEmpty;
    } else {
      Get.snackbar(
          'Error',
          res['result']['message'] ??
              I18n.of(context)?.translate('cm_find.errorMessage') ??
              '');
    }
  }

  // 获取优惠券列表
  Future<void> onGetCouponList([bool refresh = false]) async {
    if (isLoading.value) return;

    try {
      isLoading.value = true;

      if (refresh) {
        pageInfo['current'] = 1;
        hasMore.value = true;
      }

      final params = {
        'page': {
          'current': pageInfo['current'],
          'size': pageInfo['size'],
          'total': pageInfo['total'],
        },
        'ticketStatus': ticketStatus.value,
      };

      if (ticketStatus.value == 'TICKET_NOT_USE' &&
          couponType.value != 'COUPON_TYPE_ALL') {
        params['couponType'] = couponType.value;
      }

      final res = await UserAPI.useGetMyCouponDetailList(params);

      if (res['result']['code'] == 200) {
        final records = res['data'] ?? [];
        pageInfo['total'] = res['page']['total'] ?? 0;

        if (refresh) {
          couponList.value = records;
        } else {
          couponList.addAll(records);
        }

        hasMore.value = couponList.length < pageInfo['total'];

        if (hasMore.value) {
          pageInfo['current'] = pageInfo['current'] + 1;
        }
      }
    } catch (e) {
      print(e);
    } finally {
      isLoading.value = false;
    }
  }

  void _onScrollBottom() {
    if (isLoading.value || !hasMore.value) return;
    if (_scrollController.position.pixels <
        _scrollController.position.maxScrollExtent) return;

    onGetCouponList();
  }

  // 新增初始化方法
  Future<void> _initPage() async {
    await onQueryVerifyMailResult();
    isPageReady.value = true;
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: AppColors.primaryBackground,
      appBar: AppBar(
        backgroundColor: AppColors.primaryBackground,
        title: Text(I18n.of(context)?.translate('cm_coupon.myCoupon') ?? ''),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      scrollController: _scrollController,
      showScrollToTopButton: true,
      body: Obx(() {
        // 等待页面准备就绪
        if (!isPageReady.value) {
          return const Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF333333)),
            ),
          );
        }

        // 页面准备就绪后，根据状态显示对应内容
        if (isNeedMailVerify.value) {
          return EmailValidation(couponList: pendingCouponList);
        }

        return Column(
          children: [
            Container(
              color: AppColors.primaryBackground,
              child: Column(
                children: [
                  TabBar(
                    controller: _tabController,
                    labelColor: const Color(0xFF333333),
                    unselectedLabelColor: const Color(0xFF999999),
                    labelStyle: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      height: 1.2,
                    ),
                    unselectedLabelStyle: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      height: 1.2,
                    ),
                    indicatorColor: const Color(0xFF333333),
                    indicatorSize: TabBarIndicatorSize.label,
                    labelPadding: const EdgeInsets.symmetric(horizontal: 12),
                    tabs: couponTabData
                        .map((tab) => Tab(
                              height: 36,
                              child: Text(
                                I18n.of(context)
                                        ?.translate(tab['label'] ?? '') ??
                                    '',
                              ),
                            ))
                        .toList(),
                    onTap: (index) {
                      ticketStatus.value = couponTabData[index]['value'] ?? '';
                      pageInfo['current'] = 1;
                      onGetCouponList(true);
                    },
                  ),
                  if (ticketStatus.value == 'TICKET_NOT_USE')
                    Container(
                      padding: EdgeInsets.only(top: 16.sp),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: couponTypeData
                            .asMap()
                            .entries
                            .map((entry) => GestureDetector(
                                  onTap: () {
                                    couponType.value =
                                        entry.value['value'] ?? '';
                                    pageInfo['current'] = 1;
                                    onGetCouponList(true);
                                  },
                                  child: Container(
                                    margin:
                                        EdgeInsets.symmetric(horizontal: 4.sp),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8.sp, vertical: 5.sp),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        color: couponType.value ==
                                                entry.value['value']
                                            ? const Color(0xFF333333)
                                            : const Color(0xFF4D4D4D),
                                        width: couponType.value ==
                                                entry.value['value']
                                            ? 2
                                            : 1,
                                      ),
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Text(
                                      I18n.of(context)?.translate(
                                              entry.value['label'] ?? '') ??
                                          '',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: couponType.value ==
                                                entry.value['value']
                                            ? const Color(0xFF333333)
                                            : const Color(0xFF4D4D4D),
                                      ),
                                    ),
                                  ),
                                ))
                            .toList(),
                      ),
                    ),
                ],
              ),
            ),
            SizedBox(height: 16.sp),
            Expanded(
              child: Container(
                color: AppColors.primaryBackground,
                child: isMailVerified.value && couponList.isNotEmpty
                    ? ListView.builder(
                        controller: _scrollController,
                        padding: EdgeInsets.symmetric(vertical: 2.sp),
                        itemCount: couponList.length +
                            (isLoading.value ? 1 : 0) +
                            (couponList.any((coupon) =>
                                    coupon['ticketStatus'] == 'TICKET_USE')
                                ? 1
                                : 0) +
                            (couponList.any((coupon) =>
                                    coupon['ticketStatus'] ==
                                    'TICKET_LOSE_EFFICACY')
                                ? 1
                                : 0),
                        itemBuilder: (context, index) {
                          if (index < couponList.length) {
                            return Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: 12.sp,
                                vertical: 0,
                              ),
                              child: CouponCard(
                                coupon: couponList[index],
                                pageSource: 'modal',
                              ),
                            );
                          }

                          if (index == couponList.length && isLoading.value) {
                            return const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Color(0xFF333333),
                                  ),
                                ),
                              ),
                            );
                          }

                          // 计算提示信息的索引
                          int tipIndex = index -
                              couponList.length -
                              (isLoading.value ? 1 : 0);

                          // 已使用优惠券提示
                          if (tipIndex == 0 &&
                              couponList.any((coupon) =>
                                  coupon['ticketStatus'] == 'TICKET_USE')) {
                            return Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: 12.sp,
                                vertical: 8.sp,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Icon(
                                    Icons.error,
                                    size: 16,
                                    color: Color(0xFF999999),
                                  ),
                                  SizedBox(width: 4.sp),
                                  Expanded(
                                    child: Text(
                                      I18n.of(context)?.translate(
                                            'cm_coupon.usedCouponDesc',
                                          ) ??
                                          'Solo se mostrarán los cupones que hayan caducado en los últimos tres meses',
                                      style: TextStyle(
                                        color: Color(0xFF999999),
                                        fontSize: 12.sp,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }

                          // 已过期优惠券提示
                          if (tipIndex == 0 &&
                              couponList.any((coupon) =>
                                  coupon['ticketStatus'] ==
                                  'TICKET_LOSE_EFFICACY')) {
                            return Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: 12.sp,
                                vertical: 8.sp,
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Icon(
                                    Icons.error,
                                    size: 16,
                                    color: Color(0xFF999999),
                                  ),
                                  SizedBox(width: 4.sp),
                                  Expanded(
                                    child: Text(
                                      I18n.of(context)?.translate(
                                            'cm_coupon.invalidCouponDesc',
                                          ) ??
                                          'Solo se mostrarán los cupones que hayan caducado en los últimos tres meses',
                                      style: TextStyle(
                                        color: Color(0xFF999999),
                                        fontSize: 12.sp,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }
                          return const SizedBox();
                        },
                      )
                    : _buildEmptyState(),
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildEmptyState() {
    if (isLoading.value) {
      return const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            Color(0xFF333333),
          ),
        ),
      );
    }

    return Container(
      color: AppColors.primaryBackground,
      width: double.infinity,
      child: Column(
        children: [
          SizedBox(height: 40.sp),
          SvgPicture.asset(
            'assets/images/marketing/noCoupon.svg',
            width: 120,
            height: 120,
          ),
          SizedBox(height: 16.sp),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 32.sp),
            child: Text(
              I18n.of(context)?.translate(
                    ticketStatus.value == 'TICKET_NOT_USE'
                        ? 'cm_coupon.noCouponData'
                        : ticketStatus.value == 'TICKET_USE'
                            ? 'cm_coupon.noUsedCouponData'
                            : 'cm_coupon.noInvalidCouponData',
                  ) ??
                  '',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF333333),
                height: 1.4,
              ),
            ),
          ),
          if (ticketStatus.value != 'TICKET_NOT_USE')
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 30.sp, vertical: 12.sp),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 2.sp),
                    child: Icon(
                      Icons.info_outline,
                      size: 18.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
                  SizedBox(width: 2.sp),
                  Expanded(
                    child: Text(
                      I18n.of(context)?.translate(
                            ticketStatus.value == 'TICKET_USE'
                                ? 'cm_coupon.usedCouponDesc'
                                : 'cm_coupon.invalidCouponDesc',
                          ) ??
                          '',
                      textAlign: TextAlign.left,
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: const Color(0xFF999999),
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
