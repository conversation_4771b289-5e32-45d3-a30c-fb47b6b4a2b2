// import 'package:flutter/material.dart';

import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/constants/config.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MySearchBar extends StatefulWidget {
  final String? keywords;
  final double? searchWidth;
  final bool? logoShow;
  final bool homePage;

  const MySearchBar(
      {super.key,
      this.keywords,
      this.searchWidth,
      this.logoShow = true,
      this.homePage = false});

  @override
  State<MySearchBar> createState() => _MySearchBarState();
}

class _MySearchBarState extends State<MySearchBar> {
  String keywords = '';
  double screenWidth = 0.0;
  final TextEditingController _textController = TextEditingController();

  @override
  void initState() {
    super.initState();
    keywords = widget.keywords ?? '';
    _textController.text = keywords;
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.sizeOf(context).width;

    return Container(
        height: 69,
        padding: EdgeInsets.symmetric(horizontal: 2, vertical: 4),
        decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Colors.black26,
              width: 0.3,
            ),
          ),
        ),
        child: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 24,
                padding: EdgeInsets.symmetric(vertical: 2),
                child: Center(
                  child: MyCachedNetworkImage(
                    imageUrl: APP_LOGO,
                    width: 128.sp,
                    height: 24.sp,
                  ),
                ),
              ),
              Container(
                height: 32,
                padding: EdgeInsets.symmetric(horizontal: 2, vertical: 0),
                child:
                    Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                  Container(
                    margin: EdgeInsets.only(left: 2, right: 4),
                    child: GestureDetector(
                      onTap: () {
                        if (widget.homePage) {
                          NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
                        } else {
                          NavigatorUtil.pop(context);
                        }
                      },
                      child: Icon(
                        color: Colors.red.shade700,
                        widget.homePage ? Icons.home : Icons.arrow_back,
                        size: 24,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Container(
                    width: widget.searchWidth ?? screenWidth * 0.78,
                    margin: EdgeInsets.symmetric(horizontal: 4),
                    child: TextField(
                        controller: _textController,
                        onChanged: (value) {},
                        decoration: InputDecoration(
                            hintText: 'Search...',
                            hintStyle: TextStyle(fontSize: 12),
                            filled: true,
                            fillColor: Colors.grey.shade100,
                            contentPadding: EdgeInsets.symmetric(vertical: 2),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(6),
                              borderSide: BorderSide.none,
                            ),
                            // 未聚焦时的边框
                            enabledBorder: OutlineInputBorder(
                              borderSide:
                                  BorderSide(color: Colors.red, width: 0.5),
                            ),
                            // 聚焦时的边框
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                  color: Colors.red, width: 1), // 聚焦时加粗
                            ),
                            prefixIcon: Icon(Icons.search, size: 24),
                            suffixIcon: GestureDetector(
                              onTap: () {
                                NavigatorUtil.pushNamed(
                                    context, AppRoutes.ProductList,
                                    arguments: {
                                      "keyword": _textController.text,
                                    });
                              },
                              child: Container(
                                width: 48,
                                margin: EdgeInsets.only(left: 4),
                                padding: EdgeInsets.symmetric(horizontal: 3),
                                decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(4))
                                    // borderRadius: const BorderRadius.only(
                                    //   topLeft: Radius.circular(4),
                                    //   bottomLeft: Radius.circular(4),
                                    // ),
                                    ),
                                child: const Center(
                                  child: Text(
                                    '搜索',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                            )),
                        onSubmitted: (value) {
                          NavigatorUtil.pushNamed(
                              context, AppRoutes.ProductList,
                              arguments: {
                                "keyword": _textController.text,
                              });
                        }),
                  ),
                  const Spacer(), // 占据剩余空间
                  Container(
                    padding: EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: GestureDetector(
                      onTap: () {
                        // TODO 待完善
                      },
                      child: Icon(
                        Icons.camera_alt_sharp,
                        size: 24,
                      ),
                    ),
                  )
                ]),
              )
            ],
          );
        }));

    // return Container(
    //   padding: EdgeInsets.symmetric(horizontal: 0),
    //   child: Column(,
    // );
  }
}
