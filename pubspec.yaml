name: chilat2_mall_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get: ^4.6.6
  flutter_screenutil: ^5.9.3
  transparent_image: ^2.0.1
  flutter_swiper_null_safety: ^1.0.2
  cached_network_image: ^3.4.1
  webview_flutter: ^4.10.0
  flutter_inappwebview: ^6.0.0
  permission_handler: ^11.3.1
  oktoast: ^3.4.0
  dio: ^5.7.0
  tuple: ^2.0.2
  pull_to_refresh: ^2.0.0
  easy_localization: ^3.0.7
  shared_preferences: ^2.3.4
  url_launcher: ^6.3.1
  sqflite: ^2.4.1
  convert: ^3.1.2
  flutter_localizations:
    sdk: flutter
  carousel_slider: ^5.0.0
  flutter_svg: ^2.1.0
  cached_video_player_plus: ^3.0.3
  video_player: ^2.9.2
  flutter_html: ^3.0.0-beta.2
  flutter_widget_from_html_core: ^0.15.2
  flutter_staggered_grid_view: ^0.7.0
  dotted_border: ^2.0.0
  marquee: ^2.3.0
  popover: ^0.3.1
  image_picker: ^1.1.2
  flutter_image_compress: ^2.4.0
  path_provider: ^2.1.5
  flutter_native_splash: ^2.3.3
  shimmer: ^3.0.0
  crypto: ^3.0.6
  html: ^0.15.4

flutter_native_splash:
  color: "#FFFFFF"
  # 启动图片
  image: assets/images/launcher.png
  # 图片显示模式
  android_gravity: center
  ios_content_mode: center

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
flutter_icons:
  android: "ic_launcher"
  ios: false
  image_path: "assets/images/launcher.png"

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    # LOGO
    - assets/images/logo.png
    - assets/images/launcher.png
    - assets/images/common/loading.png
    - assets/images/common/loading.gif

    # common
    - assets/images/common/cart.svg
    - assets/images/common/home.svg
    - assets/images/common/search.svg
    - assets/images/common/camera.svg
    - assets/images/common/arrow-left.svg
    - assets/images/common/arrow-upward.svg
    - assets/images/common/whatsapp.svg
    - assets/images/common/whatsapp-icon-default.svg
    - assets/images/common/postcode.svg
    - assets/images/common/cart-submit-white.svg
    - assets/images/common/feedback_success.svg
    - assets/images/common/arrow-right-white.svg
    - assets/images/common/no-data.svg
    - assets/images/common/find-form.svg
    - assets/images/common/mobile-find.svg
    - assets/images/common/mobile-home.svg
    - assets/images/common/mobile-home-active.svg
    - assets/images/common/mobile-category.svg
    - assets/images/common/mobile-category-active.svg
    - assets/images/common/mobile-cart.svg
    - assets/images/common/mobile-cart-active.svg
    - assets/images/common/mobile-user.svg
    - assets/images/common/mobile-user-active.svg
    - assets/images/common/gift.webp
    - assets/images/common/logo.png

    # 导航栏
    - assets/images/nav/nav_back.png
    # tab bar
    - assets/images/tabbar/zhuye_ON.png
    - assets/images/tabbar/zhuye_off.png
    - assets/images/tabbar/fenlei_ON.png
    - assets/images/tabbar/fenlei_off.png
    - assets/images/tabbar/diangdan_ON.png
    - assets/images/tabbar/dingdan_off.png
    - assets/images/tabbar/guanli_ON.png
    - assets/images/tabbar/guanli_off.png

    # 首页
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    # 首页轮播图
    - assets/images/home/<USER>
    - assets/images/home/<USER>

    # 详情页
    - assets/images/detail/up.png
    - assets/images/detail/dowm.png

    # 供应商页面
    - assets/images/supplier/fenxiang.png
    - assets/images/supplier/beijing.png
    - assets/images/supplier/weizi.png
    - assets/images/supplier/lianxiren.png
    - assets/images/supplier/shijian.png

    # 购物车
    - assets/images/shopping_cart/bottom_check.png
    - assets/images/shopping_cart/check_un.png
    - assets/images/shopping_cart/check.png
    - assets/images/shopping_cart/down.png
    - assets/images/shopping_cart/down1.png
    - assets/images/shopping_cart/icon-sj-26.png
    - assets/images/shopping_cart/up.png
    - assets/images/shopping_cart/jiazaizhong.png
    - assets/images/shopping_cart/empty.png

    # 支付页
    - assets/images/pay/check.png
    - assets/images/pay/uncheck.png

    # 确认订单页面
    - assets/images/confirm_order/edit.png
    - assets/images/confirm_order/dingdanchenggong.png

    # 登录页面
    - assets/images/login/bg.jpg
    - assets/images/login/email.svg
    - assets/images/login/password.svg

    # 轮播图
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>
    - assets/images/home/<USER>

    # 个人中心
    - assets/images/mine/unLogin.png
    - assets/images/mine/invite.svg
    - assets/images/mine/email.svg
    - assets/images/mine/coupon.svg
    - assets/images/mine/address.svg
    - assets/images/mine/inviteCouponSelf.svg
    - assets/images/mine/inviteCouponOther.svg
    - assets/images/mine/inviteWarning.svg

      # Marketing
    - assets/images/marketing/commissionCoupon.png
    - assets/images/marketing/down.svg
    - assets/images/marketing/loginMarket.png
    - assets/images/marketing/productCoupon.png
    - assets/images/marketing/top.svg
    - assets/images/marketing/noCoupon.svg
    - assets/images/marketing/expiredCoupon.svg
    - assets/images/marketing/invalidCoupon.svg
    - assets/images/marketing/couponBase.svg

    # 图标
    - assets/images/icons/facebook.png
    - assets/images/icons/in.png
    - assets/images/icons/ins.png
    - assets/images/icons/youtube.png
    - assets/images/icons/tiktok.png
    - assets/images/icons/videos.png

    #  Order
    - assets/images/order/pay-error.svg
    - assets/images/order/pay-success.svg

    # Article
    - assets/images/article/invite-header-bg.jpg
    - assets/images/article/invite-footer-bg.jpg
    - assets/images/article/new-customer.png
    - assets/images/article/old-customer.png
    - assets/images/article/fifteen.png
    - assets/images/article/ten.png
    - assets/images/article/personal-center.png
    - assets/images/article/Visa.png
    - assets/images/article/BBVA.png
    - assets/images/article/Interbank.png
    - assets/images/article/Scotiabank.png
    - assets/images/article/Paypal.png
    - assets/images/article/Yape.png
    - assets/images/article/Plin.png
    - assets/images/article/Tunki.png
    - assets/images/article/transfer.svg
    - assets/images/article/cash.svg
    - assets/images/article/ewallet.svg
    - assets/images/article/others.svg
    - assets/images/article/mobilePayOrder.png
    - assets/images/article/Mexico.png
    - assets/images/article/Peru.png
    - assets/images/article/Argentina.png
    - assets/images/article/Chile.png
    - assets/images/article/Colombia.png
    - assets/images/article/CostaRica.png
    - assets/images/article/Ecuador.png
    - assets/images/article/Panama.png

    # icons/询盘
    - assets/icons/find/step-line.png
    - assets/icons/find/check-circle.svg
    - assets/icons/find/check.svg
    - assets/icons/find/quote.svg
    - assets/icons/find/quote-active.svg
    - assets/icons/find/uncheck-circle.svg
    - assets/icons/find/unpaid.svg
    - assets/icons/find/paid.svg
    - assets/icons/order/purchasing.svg
    - assets/icons/order/deliveringAc.svg
    # 优惠券
    - assets/icons/home/<USER>
    # 找货
    - assets/images/search/find-submitted.svg

  # 字体图标
  # see https://flutter.dev/custom-fonts/#from-packages
  fonts:
    - family: Iconfont
      fonts:
        - asset: assets/fonts/iconfont.ttf

    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
