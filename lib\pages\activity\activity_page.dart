import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/pages/product/components/category_product.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ActivityPage extends StatefulWidget {
  const ActivityPage({super.key});

  @override
  State<ActivityPage> createState() => _ActivityPageState();
}

class _ActivityPageState extends State<ActivityPage> {
  bool _isLoading = false;
  // PromotionGoodsIndexModel? pageData;
  dynamic pageData = {};
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    onPageData();
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> onPageData() async {
    try {
      setState(() => _isLoading = true);
      dynamic res = await ProductAPI.useGetPromotionGoodsIndexByCode(
          {"code": Get.arguments?['code']});
      if (res?['result']?['code'] == 200) {
        setState(() {
          pageData = res?['data'];
        });
      }
      setState(() => _isLoading = false);
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      onPopInvoked: (result) {
        // TODO 待完善
      },
      body: Stack(
        children: [
          Column(
            children: [
              SearchHeader(showHomeIcon: true),
              Expanded(
                child: _buildContent(),
              )
            ],
          ),
          // 加载指示器
          Visibility(
            visible: _isLoading,
            child: Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.2),
                child: Center(
                  child: RotationalLoadingWidget(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      controller: _scrollController,
      child: ListView.builder(
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: pageData?['selectorList']?.length ?? 0,
          itemBuilder: (context, index) {
            return CategoryProduct(
              color: Colors.red.shade500,
              data: pageData?['selectorList']?[index],
            );
          }),
    );
  }
}
