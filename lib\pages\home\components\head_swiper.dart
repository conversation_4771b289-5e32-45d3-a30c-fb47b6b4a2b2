import 'package:chilat2_mall_app/components/components.dart';
import 'package:flutter/material.dart';
import 'package:flutter_swiper_null_safety/flutter_swiper_null_safety.dart';

class HeadSwiper extends StatefulWidget {
  const HeadSwiper({super.key});

  @override
  State<HeadSwiper> createState() => _HeadSwiperState();
}

class _HeadSwiperState extends State<HeadSwiper> {
  final List<String> bannerList = [
    'https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2025/01/16/d09e5d58-db2a-4b5c-8a30-7a7638ff0bf1.jpg',
    'https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2025/01/16/95ee9660-6a8a-4d8c-b162-d56ba8185713.jpg',
    'https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2025/01/16/c75070d0-26cd-4754-8c64-a0a04a4bb769.jpg',
    'https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2025/01/16/4105d13b-be73-4e8d-9425-c9cafac38589.jpg',
    'https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2025/01/16/355d61c2-d18c-45e3-809d-db0c99bc7275.png',
  ];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 0),
      color: Colors.white,
      child: AspectRatio(
        aspectRatio: 25 / 18,
        child: Swiper(
          autoplay: true,
          autoplayDelay: 4000,
          duration: 750,
          itemCount: bannerList.length,
          itemBuilder: (BuildContext context, int index) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(2),
              child: MyCachedNetworkImage(
                imageUrl: bannerList[index],
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            );
          },
        ),
      ),
    );
  }
}
