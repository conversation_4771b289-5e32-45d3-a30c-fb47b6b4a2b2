import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/pages/product/components/product_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CategoryProduct extends StatefulWidget {
  final dynamic data;
  final Color color;
  const CategoryProduct({super.key, required this.data, required this.color});

  @override
  State<CategoryProduct> createState() => _CategoryProductState();
}

class _CategoryProductState extends State<CategoryProduct> {
  final double _widgetHeight = 230.0;
  final double _bannerHeight = 90.0;
  final double _horizonHeight = 180.0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void onSearchListClick() {
    // TODO: 搜索列表
  }

  @override
  Widget build(BuildContext context) {
    String? title;
    if (widget.data?['tagName'] != null) {
      title = widget.data?['tagName'];
    } else if (widget.data?['categoryName'] != null) {
      title = widget.data?['categoryName'];
    } else if (widget.data?['selectorName'] != null) {
      title = widget.data?['selectorName'];
    } else if (widget.data?['title'] != null) {
      title = widget.data?['title'];
    }

    return Container(
      height: widget.data?['bannerUrl'] != null ? 320 : _widgetHeight, // 设置容器高度
      margin: const EdgeInsets.symmetric(vertical: 0),
      child: Stack(
        children: <Widget>[
          // 标题和按钮
          Positioned(
            top: 5,
            left: 0,
            child: Container(
              height: widget.data?['bannerUrl'] != null ? 128 : _bannerHeight,
              width: MediaQuery.of(context).size.width,
              color: widget.data?['bannerUrl'] != null
                  ? Colors.white
                  : widget.color,
              padding: widget.data?['bannerUrl'] != null
                  ? EdgeInsets.all(0.sp)
                  : EdgeInsets.all(10.sp),
              child: widget.data?['bannerUrl'] != null
                  ? MyCachedNetworkImage(
                      imageUrl: widget.data?['bannerUrl'] ?? '',
                      width: double.infinity,
                      borderRadius: BorderRadius.circular(4),
                    )
                  : Text(
                      title ?? '',
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 16.0,
                          fontWeight: FontWeight.w500),
                    ),
            ),
          ),
          Positioned(
            top: 5,
            right: 5,
            child: widget.data?['bannerUrl'] != null
                ? SizedBox.shrink()
                : IconButton(
                    color: Colors.white,
                    icon: Icon(Icons.arrow_forward),
                    onPressed: () {
                      // 在这里添加点击按钮后的操作逻辑
                      print('IconButton clicked');
                    },
                  ),
          ),
          // 商品列表
          Positioned(
            top: widget.data?['bannerUrl'] != null ? 136 : 50.0, // 设置商品列表的顶部位置
            left: 0,
            right: 0,
            child: SizedBox(
              height: _horizonHeight, // 设置水平滚动区域的高度
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: widget.data?['goodsList']?.length,
                itemBuilder: (BuildContext context, int index) {
                  dynamic product = widget.data?['goodsList'][index] ?? {};
                  return GoodsListDataItemCard(goods: product);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
