import 'dart:convert';
import 'package:crypto/crypto.dart';

import 'package:easy_localization/easy_localization.dart';
import 'package:chilat2_mall_app/components/common_webview_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/constants/config.dart';
import 'package:chilat2_mall_app/utils/local_storage.dart';

const double designWidth = 750; //定义设计稿的宽度
const String currencyUnit = "US";
const String monetaryUnit = "US\$ ";

// 设置货币单位
String setUnit(num? val, {int? decimal = 2}) {
  if (val == null || val.isNaN) return "";

  // 处理负数的情况
  bool isNegative = val < 0;
  num absoluteValue = val.abs();

  // 使用 NumberFormat 格式化数字，添加千位分隔符和两位小数
  final formatter = NumberFormat.currency(
    locale: 'en_US',
    symbol: monetaryUnit,
    decimalDigits: decimal,
  );
  String formattedValue = formatter.format(absoluteValue);

  return (isNegative ? "- " : "") + formattedValue;
}

int crossAxisCount(double screenWidth, double unitWidth) {
  if (unitWidth <= 0) {
    return 1;
  } else {
    double result = screenWidth / unitWidth;
    return result.toInt();
  }
}

// 时间戳转日期 转北京时间 根据当前时区转时间
String timeFormatByZone(int ts, {bool showSeconds = true}) {
  if (ts == 0) {
    return "";
  }
  DateTime date =
      DateTime.fromMillisecondsSinceEpoch(ts, isUtc: true).toLocal();
  String formattedDate =
      "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}";

  if (showSeconds) {
    return "$formattedDate:${date.second.toString().padLeft(2, '0')}";
  }

  return formattedDate;
}

/// 格式化金额
String setNewUnit(dynamic val, [bool noUnit = false]) {
  if (val == null ||
      val.toString().isEmpty ||
      num.tryParse(val.toString()) == null) {
    return '';
  }

  final number = num.parse(val.toString());
  final hasDecimal = number % 1 != 0;

  // 使用 NumberFormat 格式化数字
  final formatter = NumberFormat(hasDecimal ? '#,##0.00' : '#,##0', 'en_US');
  final formattedValue = formatter.format(number);

  if (noUnit) {
    return formattedValue;
  }
  return '$monetaryUnit$formattedValue';
}

/// 浮点数四舍五入
double mathRound(dynamic val, [int precision = 2]) {
  if (val == null ||
      val.toString().isEmpty ||
      num.tryParse(val.toString()) == null) {
    return 0;
  }

  if (precision <= 0) {
    return double.parse(val.toString()).roundToDouble();
  }

  final number = double.parse(val.toString());
  final factor = pow(10, precision);
  return (number * factor).round() / factor;
}

/// 小数转换为百分比
String decimalToPercentage(dynamic decimal, [int decimals = 0]) {
  if (decimal == null ||
      decimal.toString().isEmpty ||
      num.tryParse(decimal.toString()) == null) {
    return '0%';
  }

  final percentage =
      (double.parse(decimal.toString()) * 100).toStringAsFixed(decimals);
  return '$percentage%';
}

/// 邮箱登录页面映射
final Map<String, String> emailLoginPages = {
  "gmail.com": "https://gmail.com",
  "hotmail.com": "https://outlook.com",
  "outlook.com": "https://outlook.com",
  "yahoo.com": "https://mail.yahoo.com",
  "icloud.com": "https://icloud.com/mail",
  "qq.com": "https://wx.mail.qq.com",
  "yahoo.com.mx": "https://mail.yahoo.com",
  "live.com.mx": "https://outlook.com",
  "outlook.es": "https://outlook.com",
  "yahoo.es": "https://mail.yahoo.com",
  "hotmail.es": "https://outlook.com",
  "msn.com": "https://outlook.com",
  "gmail.com.pe": "https://gmail.com",
  "aol.com": "https://mail.aol.com",
  "live.com": "https://outlook.com",
  "mail.com": "https://mail.com",
  "yahoo.com.ar": "https://mail.yahoo.com",
  "rocketmail.com": "https://mail.yahoo.com",
  "yahoo.com.pe": "https://mail.yahoo.com",
  "gmail.com.mx": "https://gmail.com",
  "googlemail.com": "https://gmail.com",
  "hmail.com": "https://hmail.com",
  "ymail.com": "https://mail.yahoo.com",
  "prodigy.net.mx": "https://telmex.com/infinitummail",
  "126.com": "https://126.com",
};

/// 导航到邮箱登录页面
Future<void> navigateToEmail([String? email]) async {
  // 如果没有提供邮箱，使用当前用户的邮箱
  if (email == null) {
    dynamic userInfo = LocalStorage().getJSON(USER_INFO);

    if (userInfo != null && userInfo is Map<String, dynamic>) {
      email = userInfo['username'] as String?;

      if (email == null || email.isEmpty) {
        email = userInfo['email'] as String?; // Try alternative email field
      }
    }
  }

  if (email == null || email.isEmpty) {
    Get.snackbar(
      'Error',
      'No valid email address found',
      backgroundColor: Colors.red[400],
      colorText: Colors.white,
      duration: Duration(seconds: 4),
      snackPosition: SnackPosition.TOP,
    );
    return;
  }

  // 提取邮箱域名
  final parts = email.split("@");
  if (parts.length != 2) {
    Get.snackbar(
      'Error',
      'Invalid email format',
      backgroundColor: Colors.red[400],
      colorText: Colors.white,
      duration: Duration(seconds: 4),
      snackPosition: SnackPosition.TOP,
    );
    return;
  }

  final domain = parts[1].toLowerCase(); // Convert to lowercase for matching
  final emailPage = emailLoginPages[domain];

  if (emailPage != null) {
    final uri = Uri.parse(emailPage);
    try {
      Get.to(() => CommonWebViewPage(title: 'Buzón', url: uri.toString()));
    } catch (e) {
      Get.snackbar(
        'Error',
        'Could not open email client',
        backgroundColor: Colors.red[400],
        colorText: Colors.white,
        duration: Duration(seconds: 4),
        snackPosition: SnackPosition.TOP,
      );
    }
  } else {
    Get.snackbar(
      'Info',
      'Please check your email at ${parts[1]}',
      backgroundColor: Colors.grey[800],
      colorText: Colors.white,
      duration: Duration(seconds: 4),
      snackPosition: SnackPosition.TOP,
    );
  }
}

/// 计算幂
double pow(num x, int exponent) {
  if (exponent == 0) return 1;
  if (exponent < 0) {
    x = 1 / x;
    exponent = -exponent;
  }

  double result = 1;
  for (int i = 0; i < exponent; i++) {
    result *= x;
  }
  return result;
}

// 获取屏幕宽度
double screenWidth(BuildContext context) {
  return MediaQuery.sizeOf(context).width;
}

// 获取屏幕高度
double screenHeight(BuildContext context) {
  return MediaQuery.sizeOf(context).height;
}

// 定义一个函数来计算 rem 值
double rem(double value, BuildContext context) {
  // 获取当前设备的屏幕宽度
  double screenWidth = MediaQuery.sizeOf(context).width;
  // 计算缩放比例
  double scale = screenWidth / designWidth;
  // 根据缩放比例计算实际尺寸
  return value * scale;
}

// 消息提示
void showSuccessMessage(String message, {Widget? icon, int? duration}) {
  Get.snackbar('El éxito', message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.white,
      colorText: Colors.black,
      duration: Duration(seconds: duration ?? 3),
      icon: icon);
}

void showErrorMessage(String message, {Widget? icon, int? duration}) {
  Get.snackbar(
    'El fracaso',
    message,
    icon: icon,
    snackPosition: SnackPosition.TOP,
    backgroundColor: Colors.red,
    colorText: Colors.white,
    duration: Duration(seconds: duration ?? 3),
  );
}

bool isNotEmpty(dynamic value) {
  if (value == null) {
    return false;
  }
  if (value is String) {
    return value.isNotEmpty;
  } else if (value is List) {
    return value.isNotEmpty;
  } else if (value is Map) {
    return value.isNotEmpty;
  } else if (value is int) {
    return value > 0;
  } else if (value is double) {
    return value > 0.0;
  } else if (value is bool) {
    return value == true;
  }
  return false;
}

// 简易哈希生成---唯一性要求不高
String genSimpleHash(List<dynamic> values) {
  // 把 dynamic 类型转换为可哈希的字符串表示
  final hashableValues = values.map((value) {
    if (value == null) return 'null';
    if (value is String) return value;
    if (value is num || value is bool) return value.toString();

    // 针对复杂对象，转为 JSON 字符串
    try {
      return jsonEncode(value);
    } catch (e) {
      return value.toString(); // 转换失败时，使用默认的 toString()
    }
  }).toList();

  // 生成组合哈希值
  return Object.hashAll(hashableValues).toString();
}

// 加密哈希生成--高唯一性的哈希值
String genSecureHash(List<dynamic> values) {
  // 把 dynamic 类型转换为字符串
  final stringValues = values.map((value) {
    if (value == null) return 'null';
    if (value is String) return value;
    if (value is num || value is bool) return value.toString();

    try {
      return jsonEncode(value);
    } catch (e) {
      return value.toString();
    }
  }).join('|'); // 用分隔符连接所有字符串

  // 计算 SHA-256 哈希
  final bytes = utf8.encode(stringValues);
  final digest = sha256.convert(bytes);

  return digest.toString();
}

// 对象数组去重
List<T> distinctBy<T, K>(List<T> list, K Function(T) getKey) {
  final seen = <K>{};
  return list.where((element) => seen.add(getKey(element))).toList();
}
