import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/services/order.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import 'package:chilat2_mall_app/components/order_cancel_drawer.dart';

class MineOrderPage extends StatefulWidget {
  const MineOrderPage({super.key});

  @override
  State<MineOrderPage> createState() => _MineOrderPageState();
}

class _MineOrderPageState extends State<MineOrderPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  // 为每个Tab创建独立的ScrollController
  final List<ScrollController> _scrollControllers = [];
  final List<RefreshController> _refreshControllers = [];

  // 简单的Tab数据管理
  final Map<String, Map<String, dynamic>> _tabDataMap = {};

  String get currentStatus => orderTabData[_tabController.index]['value']!;

  Map<String, dynamic> get currentTabData {
    final status = currentStatus;
    if (!_tabDataMap.containsKey(status)) {
      _tabDataMap[status] = {
        'orderList': [],
        'pageInfo': {'current': 1, 'size': 5},
        'isLoading': false,
        'noMoreData': false,
      };
    }
    return _tabDataMap[status]!;
  }

  // 订单状态列表
  final List<Map<String, String>> orderTabData = [
    {
      'value': 'MALL_STATUS_ALL',
      'label': 'cm_order.statusALL',
    },
    {
      'value': 'MALL_WAIT_PAY',
      'label': 'cm_order.waitPayMoney',
    },
    {
      'value': 'MALL_PURCHASING',
      'label': 'cm_order.purchasing',
    },
    {
      'value': 'MALL_INTERNATIONAL_FEE',
      'label': 'cm_order.internationalFee',
    },
    {
      'value': 'MALL_WAIT_SEND_OUT',
      'label': 'cm_order.waitSendOut',
    },
    {
      'value': 'MALL_TRANSPORTING',
      'label': 'cm_order.transporting',
    },
    {
      'value': 'MALL_CUSTOMS_CLEARING',
      'label': 'cm_order.customsClearing',
    },
    {
      'value': 'MALL_DELIVERING',
      'label': 'cm_order.delivering',
    },
    {
      'value': 'MALL_USER_SIGNED',
      'label': 'cm_order.userSigned',
    },
    {
      'value': 'MALL_CANCELED',
      'label': 'cm_order.canceled',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: orderTabData.length,
      vsync: this,
    );
    _pageController = PageController(initialPage: 0);
    _tabController.addListener(_handleTabChange);

    // 初始化每个Tab的ScrollController和RefreshController
    for (int i = 0; i < orderTabData.length; i++) {
      final scrollController = ScrollController();
      scrollController.addListener(() => _onScrollBottom(i));
      _scrollControllers.add(scrollController);
      _refreshControllers.add(RefreshController());
    }

    _onGetOrderList();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    for (var controller in _scrollControllers) {
      controller.dispose();
    }
    for (var controller in _refreshControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _handleTabChange() {
    if (!_tabController.indexIsChanging) return;

    // 同步PageView
    if (_pageController.hasClients) {
      _pageController.animateToPage(
        _tabController.index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }

    // 如果当前Tab没有数据，则获取数据
    if (currentTabData['orderList'].isEmpty && !currentTabData['isLoading']) {
      _onGetOrderList();
    }
  }

  Future<void> _onGetOrderList({bool isLoadMore = false}) async {
    final tabData = currentTabData;

    if (tabData['isLoading'] || (!isLoadMore && tabData['noMoreData'])) {
      return;
    }

    if (!isLoadMore) {
      setState(() => tabData['isLoading'] = true);
    }

    await _performOrderListRequest(currentStatus, tabData,
        isLoadMore: isLoadMore);
  }

  Future<void> _onGetOrderListForTab(String tabStatus,
      {bool isLoadMore = false}) async {
    // 获取指定Tab的数据
    if (!_tabDataMap.containsKey(tabStatus)) {
      _tabDataMap[tabStatus] = {
        'orderList': [],
        'pageInfo': {'current': 1, 'size': 5},
        'isLoading': false,
        'noMoreData': false,
      };
    }

    final tabData = _tabDataMap[tabStatus]!;

    if (tabData['isLoading'] || (!isLoadMore && tabData['noMoreData'])) {
      return;
    }

    if (!isLoadMore) {
      setState(() => tabData['isLoading'] = true);
    }

    await _performOrderListRequest(tabStatus, tabData, isLoadMore: isLoadMore);
  }

  Future<void> _performOrderListRequest(
      String status, Map<String, dynamic> tabData,
      {bool isLoadMore = false}) async {
    try {
      final statusMap = {
        'MALL_WAIT_PAY': ['MALL_WAIT_PAY_PRODUCT', 'MALL_WAIT_PAY_ALL_FEE'],
        'MALL_INTERNATIONAL_FEE': [
          'MALL_WAIT_CAL_INTER_FEE',
          'MALL_WAIT_PAY_INTER_FEE'
        ],
      };

      final Map<String, dynamic> requestData = {
        'page': tabData['pageInfo'],
      };

      if (status != 'MALL_STATUS_ALL') {
        requestData['mallOrderStatusList'] = statusMap[status] ?? [status];
      }

      final res = await OrderService.useGetOrderList(requestData);

      if (res['result']['code'] == 200) {
        final newOrders = res['data']['orderList'] as List;
        if (newOrders.isEmpty) {
          setState(() => tabData['noMoreData'] = true);
        } else {
          setState(() {
            if (isLoadMore) {
              tabData['orderList'].addAll(newOrders);
            } else {
              tabData['orderList'] = newOrders;
            }
            tabData['pageInfo'] = res['data']['page'];
          });
        }
      } else {
        Get.snackbar(
          'Error',
          res['result']['message'] ??
              I18n.of(context)?.translate('cm_order.getOrderListFailed') ??
              '获取订单列表失败',
          snackPosition: SnackPosition.TOP,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        I18n.of(context)?.translate('cm_order.getOrderListFailed') ??
            '获取订单列表失败',
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      setState(() => tabData['isLoading'] = false);

      // 更新刷新控制器状态
      if (isLoadMore) {
        if (tabData['noMoreData']) {
          _refreshControllers[_tabController.index].loadNoData();
        } else {
          _refreshControllers[_tabController.index].loadComplete();
        }
      } else {
        _refreshControllers[_tabController.index].refreshCompleted();
      }
    }
  }

  void _onScrollBottom(int tabIndex) {
    final scrollController = _scrollControllers[tabIndex];
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 50) {
      final status = orderTabData[tabIndex]['value']!;
      final tabData = _tabDataMap[status];
      if (tabData != null && !tabData['noMoreData'] && !tabData['isLoading']) {
        tabData['pageInfo']['current']++;
        _onGetOrderListForTab(status, isLoadMore: true);
      }
    }
  }

  void _showCancelDrawer(String orderNo) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (context) => OrderCancelDrawer(
        orderNo: orderNo,
        onCancelSuccess: () => _onGetOrderList(),
      ),
    );
  }

  String _getSkuName(List<dynamic> specs) {
    return specs
        .map((spec) => '${spec['specName']}: ${spec['itemName']}')
        .join('; ');
  }

  Widget _buildSkuItem(Map<String, dynamic> sku) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MyCachedNetworkImage(
            imageUrl: sku['picUrl'] ?? '',
            width: 80.sp,
            height: 80.sp,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  sku['goodsName'] ?? '',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 4),
                Text(
                  _getSkuName(sku['specList'] ?? []),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      setUnit(sku['unitPrice']),
                      style: const TextStyle(
                        color: Color(0xFFE50013),
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      'x${sku['count']}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItem(Map<String, dynamic> order) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.sp),
      color: Colors.white,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(10.sp),
            decoration: BoxDecoration(
              color: Color(0xFFF0FFF0),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.sp),
                topRight: Radius.circular(8.sp),
              ),
              border: Border(
                bottom: BorderSide(
                  color: Color.fromRGBO(52, 211, 153, 1),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  order['orderNo'] ?? '',
                  style: TextStyle(fontSize: 13.sp),
                ),
                Expanded(
                  child: Text(
                    order['statusDesc'] ?? '',
                    style: TextStyle(
                      color: const Color(0xFFE50013),
                      fontSize: 12.sp,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.sp, vertical: 6.sp),
            child: Column(
              children: [
                ...(order['skuList'] as List? ?? [])
                    .map((sku) => _buildSkuItem(sku)),
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            padding: EdgeInsets.symmetric(horizontal: 14.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (_shouldShowCancelButton(order['mallOrderStatus']))
                  OutlinedButton(
                    onPressed: () => _showCancelDrawer(order['orderNo']),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFFE50013),
                      backgroundColor: const Color(0xFFF6D2D4),
                      minimumSize: Size(0, 24.sp),
                      padding: EdgeInsets.symmetric(horizontal: 14.sp),
                      side: BorderSide.none,
                    ),
                    child: Text(
                      I18n.of(context)?.translate('cm_order.orderCancel') ??
                          '取消订单',
                      style: TextStyle(fontSize: 13.sp),
                      textAlign: TextAlign.center,
                    ),
                  ),
                if (_shouldShowPayButton(order['mallOrderStatus']))
                  Padding(
                    padding: EdgeInsets.only(left: 8.sp),
                    child: ElevatedButton(
                      onPressed: () => Get.toNamed(AppRoutes.OrderDetail,
                          arguments: {'orderNo': order['orderNo']}),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFE50013),
                        minimumSize: Size(0, 24.sp),
                        padding: EdgeInsets.symmetric(horizontal: 14.sp),
                        side: BorderSide.none,
                      ),
                      child: Text(
                        I18n.of(context)?.translate('cm_order.orderPay') ??
                            '去支付',
                        style: TextStyle(fontSize: 13.sp, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                Padding(
                  padding: EdgeInsets.only(left: 8.sp),
                  child: ElevatedButton(
                    onPressed: () => Get.toNamed(AppRoutes.OrderDetail,
                        arguments: {'orderNo': order['orderNo']}),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFE50013),
                      minimumSize: Size(0, 24.sp),
                      padding: EdgeInsets.symmetric(horizontal: 14.sp),
                      side: BorderSide.none,
                    ),
                    child: Text(
                      I18n.of(context)?.translate('cm_order.orderDetail') ??
                          '查看详情',
                      style: TextStyle(fontSize: 13.sp, color: Colors.white),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: const Color(0xFFF2F2F2),
      showScrollToTopButton: true,
      scrollController: _scrollControllers.isNotEmpty
          ? _scrollControllers[_tabController.index]
          : null,
      appBar: AppBar(
        title: Text(I18n.of(context)?.translate('cm_order.myOrder') ?? '我的订单'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabAlignment: TabAlignment.center,
          tabs: orderTabData.map((tab) {
            return Tab(
              text: I18n.of(context)?.translate(tab['label']!) ?? '',
            );
          }).toList(),
        ),
        centerTitle: true,
      ),
      body: PageView.builder(
        controller: _pageController,
        physics: const ClampingScrollPhysics(),
        onPageChanged: (index) {
          _tabController.animateTo(index);
        },
        itemCount: orderTabData.length,
        itemBuilder: (context, index) {
          final status = orderTabData[index]['value']!;
          return OrderTabContent(
            key: ValueKey(status),
            status: status,
            refreshController: _refreshControllers[index],
            scrollController: _scrollControllers[index],
            onGetOrderList: (String tabStatus, {bool isLoadMore = false}) =>
                _onGetOrderListForTab(tabStatus, isLoadMore: isLoadMore),
            onShowCancelDrawer: _showCancelDrawer,
            tabDataMap: _tabDataMap,
          );
        },
      ),
    );
  }

  bool _shouldShowCancelButton(String? status) {
    return status == 'MALL_WAIT_PAY_ALL_FEE' ||
        status == 'MALL_WAIT_PAY_PRODUCT';
  }

  bool _shouldShowPayButton(String? status) {
    return status == 'MALL_WAIT_PAY_ALL_FEE' ||
        status == 'MALL_WAIT_PAY_PRODUCT' ||
        status == 'MALL_WAIT_PAY_INTER_FEE';
  }
}

// 独立的订单项Widget，提升性能
class OrderItemWidget extends StatelessWidget {
  final Map<String, dynamic> order;
  final Function(String) onShowCancelDrawer;

  const OrderItemWidget({
    Key? key,
    required this.order,
    required this.onShowCancelDrawer,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.sp),
      color: Colors.white,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.sp),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(10.sp),
            decoration: BoxDecoration(
              color: Color(0xFFF0FFF0),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.sp),
                topRight: Radius.circular(8.sp),
              ),
              border: Border(
                bottom: BorderSide(
                  color: Color.fromRGBO(52, 211, 153, 1),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  order['orderNo'] ?? '',
                  style: TextStyle(fontSize: 13.sp),
                ),
                Expanded(
                  child: Text(
                    order['statusDesc'] ?? '',
                    style: TextStyle(
                      color: const Color(0xFFE50013),
                      fontSize: 12.sp,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.sp, vertical: 6.sp),
            child: Column(
              children: [
                ...(order['skuList'] as List? ?? [])
                    .map((sku) => _buildSkuItem(sku)),
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            padding: EdgeInsets.symmetric(horizontal: 14.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (_shouldShowCancelButton(order['mallOrderStatus']))
                  OutlinedButton(
                    onPressed: () => onShowCancelDrawer(order['orderNo']),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFFE50013),
                      backgroundColor: const Color(0xFFF6D2D4),
                      minimumSize: Size(0, 24.sp),
                      padding: EdgeInsets.symmetric(horizontal: 14.sp),
                      side: BorderSide.none,
                    ),
                    child: Text(
                      I18n.of(context)?.translate('cm_order.orderCancel') ??
                          '取消订单',
                      style: TextStyle(fontSize: 13.sp),
                      textAlign: TextAlign.center,
                    ),
                  ),
                if (_shouldShowPayButton(order['mallOrderStatus']))
                  Padding(
                    padding: EdgeInsets.only(left: 8.sp),
                    child: ElevatedButton(
                      onPressed: () => Get.toNamed(AppRoutes.OrderDetail,
                          arguments: {'orderNo': order['orderNo']}),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFE50013),
                        minimumSize: Size(0, 24.sp),
                        padding: EdgeInsets.symmetric(horizontal: 14.sp),
                        side: BorderSide.none,
                      ),
                      child: Text(
                        I18n.of(context)?.translate('cm_order.orderPay') ??
                            '去支付',
                        style: TextStyle(fontSize: 13.sp, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                Padding(
                  padding: EdgeInsets.only(left: 8.sp),
                  child: ElevatedButton(
                    onPressed: () => Get.toNamed(AppRoutes.OrderDetail,
                        arguments: {'orderNo': order['orderNo']}),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFE50013),
                      minimumSize: Size(0, 24.sp),
                      padding: EdgeInsets.symmetric(horizontal: 14.sp),
                      side: BorderSide.none,
                    ),
                    child: Text(
                      I18n.of(context)?.translate('cm_order.orderDetail') ??
                          '查看详情',
                      style: TextStyle(fontSize: 13.sp, color: Colors.white),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkuItem(Map<String, dynamic> sku) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MyCachedNetworkImage(
            imageUrl: sku['picUrl'] ?? '',
            width: 80,
            height: 80,
            fit: BoxFit.cover,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  sku['goodsName'] ?? '',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 4),
                Text(
                  _getSkuName(sku['specList'] ?? []),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      setUnit(sku['unitPrice']),
                      style: const TextStyle(
                        color: Color(0xFFE50113),
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      'x${sku['count']}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getSkuName(List<dynamic> specs) {
    return specs
        .map((spec) => '${spec['specName']}: ${spec['itemName']}')
        .join('; ');
  }

  bool _shouldShowCancelButton(String? status) {
    return status == 'MALL_WAIT_PAY_ALL_FEE' ||
        status == 'MALL_WAIT_PAY_PRODUCT';
  }

  bool _shouldShowPayButton(String? status) {
    return status == 'MALL_WAIT_PAY_ALL_FEE' ||
        status == 'MALL_WAIT_PAY_PRODUCT' ||
        status == 'MALL_WAIT_PAY_INTER_FEE';
  }
}

// 独立的Tab内容Widget
class OrderTabContent extends StatefulWidget {
  final String status;
  final RefreshController refreshController;
  final ScrollController scrollController;
  final Function(String status, {bool isLoadMore}) onGetOrderList;
  final Function(String) onShowCancelDrawer;
  final Map<String, Map<String, dynamic>> tabDataMap;

  const OrderTabContent({
    Key? key,
    required this.status,
    required this.refreshController,
    required this.scrollController,
    required this.onGetOrderList,
    required this.onShowCancelDrawer,
    required this.tabDataMap,
  }) : super(key: key);

  @override
  State<OrderTabContent> createState() => _OrderTabContentState();
}

class _OrderTabContentState extends State<OrderTabContent> {
  Map<String, dynamic> get tabData {
    if (!widget.tabDataMap.containsKey(widget.status)) {
      widget.tabDataMap[widget.status] = {
        'orderList': [],
        'pageInfo': {'current': 1, 'size': 5},
        'isLoading': false,
        'noMoreData': false,
      };
    }
    return widget.tabDataMap[widget.status]!;
  }

  @override
  void initState() {
    super.initState();
    // 如果当前Tab没有数据，则获取数据
    if (tabData['orderList'].isEmpty && !tabData['isLoading']) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onGetOrderList(widget.status);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SmartRefresher(
      controller: widget.refreshController,
      enablePullDown: true,
      enablePullUp: true,
      scrollController: widget.scrollController,
      // 优化滚动物理效果，避免与TabBarView冲突
      physics: const ClampingScrollPhysics(),
      onRefresh: () async {
        setState(() {
          tabData['pageInfo']['current'] = 1;
          tabData['orderList'] = [];
          tabData['noMoreData'] = false;
        });
        await widget.onGetOrderList(widget.status);
      },
      onLoading: () async {
        if (!tabData['noMoreData']) {
          final nextPage = tabData['pageInfo']['current'] + 1;
          setState(() {
            tabData['pageInfo']['current'] = nextPage;
          });
          await widget.onGetOrderList(widget.status, isLoadMore: true);
        }
      },
      child: _buildListContent(),
    );
  }

  Widget _buildListContent() {
    if (tabData['isLoading'] && tabData['orderList'].isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    // 空数据状态
    if (!tabData['isLoading'] && tabData['orderList'].isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.inbox_outlined, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              I18n.of(context)?.translate('cm_order.noData') ?? '订单列表为空',
              style: TextStyle(fontSize: 14.sp, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    // 有数据状态
    return ListView.builder(
      controller: widget.scrollController,
      padding: EdgeInsets.all(16.sp),
      itemCount: tabData['orderList'].length,
      physics: const ClampingScrollPhysics(),
      cacheExtent: 300,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      itemBuilder: (context, index) {
        final order = tabData['orderList'][index];
        return OrderItemWidget(
          key: ValueKey(order['orderNo']),
          order: order,
          onShowCancelDrawer: widget.onShowCancelDrawer,
        );
      },
    );
  }
}
