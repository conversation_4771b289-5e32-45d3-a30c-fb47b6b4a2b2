import 'package:flutter/material.dart';

class NewsItem {
  IconData? icon;
  String? title;
  Widget page;
  Color? bgcolor;

  NewsItem({this.icon, this.title, required this.page, this.bgcolor});
}

class ProcessItem {
  String title;
  IconData icon;
  String iconActive;
  List<String> contents;

  ProcessItem(
      {required this.title,
      required this.icon,
      required this.iconActive,
      required this.contents});
}

class ChooseItem {
  String title;
  String desc;

  ChooseItem({
    required this.title,
    required this.desc,
  });
}

class BrandListElement {
  String id;
  String name;
  String icon;

  BrandListElement({
    required this.id,
    required this.name,
    required this.icon,
  });

  factory BrandListElement.fromJson(Map<String, dynamic> json) {
    return BrandListElement(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      icon: json['icon'] ?? '',
    );
  }
}
